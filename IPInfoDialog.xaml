<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="InzoIB_Simple.IPInfoDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معلومات IP والشبكة - Inzo IB"
        Height="600" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="#1a1a2e"
        Foreground="White"
        FontFamily="Segoe UI">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="📍 معلومات IP والشبكة" 
                       FontSize="18" FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       Foreground="#4CAF50" Margin="0,0,0,10"/>
            
            <TextBlock Text="معلومات مفصلة عن الشبكة والاتصال مع إمكانية النسخ"
                       FontSize="12" 
                       HorizontalAlignment="Center" 
                       Foreground="#ccc" 
                       TextWrapping="Wrap"/>
        </StackPanel>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">

                <!-- معلومات الجهاز -->
                <GroupBox Header="🖥️ معلومات الجهاز" 
                          Foreground="White" BorderBrush="#4CAF50" 
                          Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- اسم الجهاز -->
                        <TextBlock Grid.Row="0" Grid.Column="0" 
                                  Text="اسم الجهاز:" 
                                  VerticalAlignment="Center" 
                                  Margin="0,0,10,5" FontWeight="Bold"/>
                        <TextBox x:Name="MachineNameTextBox" Grid.Row="0" Grid.Column="1"
                                Background="#2d2d44" Foreground="White" 
                                BorderBrush="#4CAF50" IsReadOnly="True"
                                Padding="5" Margin="0,0,10,5"/>
                        <Button x:Name="CopyMachineNameButton" Grid.Row="0" Grid.Column="2"
                                Content="📋 نسخ" 
                                Background="#4CAF50" Foreground="White"
                                Padding="8,4" Margin="0,0,0,5"
                                Click="CopyMachineName_Click"/>

                        <!-- اسم المستخدم -->
                        <TextBlock Grid.Row="1" Grid.Column="0" 
                                  Text="اسم المستخدم:" 
                                  VerticalAlignment="Center" 
                                  Margin="0,0,10,5"/>
                        <TextBox x:Name="UserNameTextBox" Grid.Row="1" Grid.Column="1"
                                Background="#2d2d44" Foreground="White" 
                                BorderBrush="#4CAF50" IsReadOnly="True"
                                Padding="5" Margin="0,0,10,5"/>

                        <!-- نظام التشغيل -->
                        <TextBlock Grid.Row="2" Grid.Column="0" 
                                  Text="نظام التشغيل:" 
                                  VerticalAlignment="Center" 
                                  Margin="0,0,10,0"/>
                        <TextBox x:Name="OSVersionTextBox" Grid.Row="2" Grid.Column="1"
                                Background="#2d2d44" Foreground="White" 
                                BorderBrush="#4CAF50" IsReadOnly="True"
                                Padding="5" Margin="0,0,10,0"/>
                    </Grid>
                </GroupBox>

                <!-- عناوين IP -->
                <GroupBox Header="🌐 عناوين IP" 
                          Foreground="White" BorderBrush="#2196F3" 
                          Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- العنوان المحلي -->
                        <TextBlock Grid.Row="0" Grid.Column="0" 
                                  Text="العنوان المحلي:" 
                                  VerticalAlignment="Center" 
                                  Margin="0,0,10,5" FontWeight="Bold"/>
                        <TextBox x:Name="LocalIPTextBox" Grid.Row="0" Grid.Column="1"
                                Background="#2d2d44" Foreground="White" 
                                BorderBrush="#2196F3" IsReadOnly="True"
                                Padding="5" Margin="0,0,10,5"/>
                        <Button x:Name="CopyLocalIPButton" Grid.Row="0" Grid.Column="2"
                                Content="📋 نسخ" 
                                Background="#2196F3" Foreground="White"
                                Padding="8,4" Margin="0,0,0,5"
                                Click="CopyLocalIP_Click"/>

                        <!-- العنوان الخارجي -->
                        <TextBlock Grid.Row="1" Grid.Column="0" 
                                  Text="العنوان الخارجي:" 
                                  VerticalAlignment="Center" 
                                  Margin="0,0,10,5" FontWeight="Bold"/>
                        <TextBox x:Name="ExternalIPTextBox" Grid.Row="1" Grid.Column="1"
                                Background="#2d2d44" Foreground="White" 
                                BorderBrush="#FF9800" IsReadOnly="True"
                                Padding="5" Margin="0,0,10,5"/>
                        <Button x:Name="CopyExternalIPButton" Grid.Row="1" Grid.Column="2"
                                Content="📋 نسخ" 
                                Background="#FF9800" Foreground="White"
                                Padding="8,4" Margin="0,0,0,5"
                                Click="CopyExternalIP_Click"/>

                        <!-- الشبكة المحلية -->
                        <TextBlock Grid.Row="2" Grid.Column="0" 
                                  Text="الشبكة المحلية:" 
                                  VerticalAlignment="Center" 
                                  Margin="0,0,10,0"/>
                        <TextBox x:Name="SubnetTextBox" Grid.Row="2" Grid.Column="1"
                                Background="#2d2d44" Foreground="White" 
                                BorderBrush="#9C27B0" IsReadOnly="True"
                                Padding="5" Margin="0,0,10,0"/>
                    </Grid>
                </GroupBox>

                <!-- معلومات المزامنة -->
                <GroupBox Header="🔗 معلومات المزامنة" 
                          Foreground="White" BorderBrush="#FF9800" 
                          Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <Grid Margin="0,0,0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="حالة الخادم:" Margin="0,0,10,0"/>
                            <TextBlock x:Name="ServerStatusTextBlock" Grid.Column="1" FontWeight="Bold"/>
                        </Grid>
                        
                        <Grid Margin="0,0,0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="المنفذ المستخدم:" Margin="0,0,10,0"/>
                            <TextBlock x:Name="PortTextBlock" Grid.Column="1"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="الأجهزة المتصلة:" Margin="0,0,10,0"/>
                            <TextBlock x:Name="ConnectedDevicesTextBlock" Grid.Column="1" FontWeight="Bold"/>
                        </Grid>
                        
                        <ListBox x:Name="DevicesListBox" 
                                Background="#2d2d44" 
                                Foreground="White"
                                BorderBrush="#FF9800"
                                Margin="0,10,0,0"
                                MaxHeight="100"/>
                    </StackPanel>
                </GroupBox>

                <!-- الشبكات الخارجية -->
                <GroupBox Header="🌍 الشبكات الخارجية" 
                          Foreground="White" BorderBrush="#9C27B0" 
                          Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="العدد:" Margin="0,0,10,0"/>
                            <TextBlock x:Name="ExternalNetworksCountTextBlock" Grid.Column="1" FontWeight="Bold"/>
                        </Grid>
                        
                        <ListBox x:Name="ExternalNetworksListBox" 
                                Background="#2d2d44" 
                                Foreground="White"
                                BorderBrush="#9C27B0"
                                MaxHeight="120">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="5">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding IP}" FontWeight="Bold"/>
                                            <TextBlock Text="{Binding Description}" FontSize="10" Foreground="#ccc"/>
                                        </StackPanel>
                                        <TextBlock Grid.Column="1" Text="{Binding StatusIcon}" FontSize="16" VerticalAlignment="Center"/>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </GroupBox>

                <!-- نصائح -->
                <GroupBox Header="💡 نصائح مفيدة" 
                          Foreground="White" BorderBrush="#607D8B" 
                          Margin="0,0,0,15">
                    <TextBlock Margin="10" TextWrapping="Wrap" LineHeight="20">
                        <Run Text="• استخدم العنوان المحلي للمزامنة في نفس الشبكة"/><LineBreak/>
                        <Run Text="• استخدم العنوان الخارجي للأجهزة في شبكات أخرى"/><LineBreak/>
                        <Run Text="• تأكد من فتح المنفذ 8080 في جدار الحماية"/><LineBreak/>
                        <Run Text="• انسخ اسم الجهاز لسهولة التعرف عليه في الشبكة"/>
                    </TextBlock>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                   HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button x:Name="RefreshButton" Content="🔄 تحديث" 
                    Background="#2196F3" Foreground="White"
                    Padding="15,8" Margin="10,0"
                    Click="Refresh_Click"/>
            <Button x:Name="CloseButton" Content="❌ إغلاق" 
                    Background="#757575" Foreground="White"
                    Padding="15,8" Margin="10,0"
                    Click="Close_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
