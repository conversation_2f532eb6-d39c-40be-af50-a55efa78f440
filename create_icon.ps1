# إنشاء أيقونة بسيطة لـ Inzo IB
Add-Type -AssemblyName System.Drawing

# إنشاء bitmap بحجم 32x32
$bitmap = New-Object System.Drawing.Bitmap(32, 32)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# تعبئة الخلفية باللون الأزرق الداكن
$brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(22, 22, 66))
$graphics.FillRectangle($brush, 0, 0, 32, 32)

# إضافة نص "IB" باللون الأبيض
$font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$graphics.DrawString("IB", $font, $textBrush, 4, 8)

# حفظ كـ PNG أولاً
$bitmap.Save("inzo_temp.png", [System.Drawing.Imaging.ImageFormat]::Png)

# تنظيف الموارد
$graphics.Dispose()
$bitmap.Dispose()
$brush.Dispose()
$textBrush.Dispose()
$font.Dispose()

Write-Host "Icon created successfully"
