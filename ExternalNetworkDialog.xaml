<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="InzoIB_Simple.ExternalNetworkDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات الشبكة الخارجية - Inzo IB"
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="#1a1a2e"
        Foreground="White"
        FontFamily="Segoe UI">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان والوصف -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="🌐 إعدادات الشبكة الخارجية" 
                       FontSize="18" FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       Foreground="#4CAF50" Margin="0,0,0,10"/>
            
            <TextBlock Text="أضف عناوين IP للأجهزة في شبكات أخرى للمزامنة عبر الإنترنت"
                       FontSize="12" 
                       HorizontalAlignment="Center" 
                       Foreground="#ccc" 
                       TextWrapping="Wrap"/>
        </StackPanel>

        <!-- قائمة العناوين الحالية -->
        <GroupBox Grid.Row="1" Header="العناوين المضافة" 
                  Foreground="White" BorderBrush="#4CAF50" 
                  Margin="0,0,0,15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- قائمة العناوين -->
                <ListBox x:Name="IPListBox" Grid.Row="0"
                         Background="#2d2d44" 
                         Foreground="White"
                         BorderBrush="#4CAF50"
                         Margin="5"
                         MinHeight="150">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{Binding IP}" FontWeight="Bold" FontSize="12"/>
                                    <TextBlock Text="{Binding Description}" FontSize="10" Foreground="#ccc"/>
                                </StackPanel>

                                <TextBlock Grid.Column="1" 
                                          Text="{Binding Status}" 
                                          FontSize="10" 
                                          Margin="10,0"
                                          VerticalAlignment="Center"/>

                                <Button Grid.Column="2" 
                                        Content="🗑️" 
                                        Width="25" Height="25"
                                        Background="#f44336" 
                                        Foreground="White"
                                        BorderThickness="0"
                                        Click="RemoveIP_Click"
                                        Tag="{Binding}"
                                        ToolTip="حذف العنوان"/>
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <!-- أزرار الإدارة -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" 
                           HorizontalAlignment="Right" Margin="5">
                    <Button x:Name="TestAllButton" Content="🔍 اختبار الكل" 
                            Background="#FF9800" Foreground="White"
                            Padding="8,4" Margin="5,0"
                            Click="TestAllIPs_Click"/>
                    <Button x:Name="ClearAllButton" Content="🗑️ حذف الكل" 
                            Background="#f44336" Foreground="White"
                            Padding="8,4" Margin="5,0"
                            Click="ClearAllIPs_Click"/>
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- إضافة عنوان جديد -->
        <GroupBox Grid.Row="2" Header="إضافة عنوان جديد" 
                  Foreground="White" BorderBrush="#4CAF50" 
                  Margin="0,0,0,15">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- عنوان IP -->
                <TextBlock Grid.Row="0" Grid.Column="0" 
                          Text="عنوان IP:" 
                          VerticalAlignment="Center" 
                          Margin="0,0,10,5"/>
                <TextBox x:Name="IPTextBox" Grid.Row="0" Grid.Column="1"
                         Background="#2d2d44" 
                         Foreground="White" 
                         BorderBrush="#4CAF50"
                         Padding="5" Margin="0,0,10,5"
                         ToolTip="مثال: ************* أو example.com"/>
                <Button x:Name="TestIPButton" Grid.Row="0" Grid.Column="2"
                        Content="🔍 اختبار" 
                        Background="#2196F3" Foreground="White"
                        Padding="8,4" Margin="0,0,0,5"
                        Click="TestIP_Click"/>

                <!-- الوصف -->
                <TextBlock Grid.Row="1" Grid.Column="0" 
                          Text="الوصف:" 
                          VerticalAlignment="Center" 
                          Margin="0,0,10,5"/>
                <TextBox x:Name="DescriptionTextBox" Grid.Row="1" Grid.Column="1"
                         Background="#2d2d44" 
                         Foreground="White" 
                         BorderBrush="#4CAF50"
                         Padding="5" Margin="0,0,10,5"
                         ToolTip="مثال: جهاز المكتب، لابتوب المنزل"/>

                <!-- زر الإضافة -->
                <Button x:Name="AddIPButton" Grid.Row="2" Grid.Column="1"
                        Content="➕ إضافة العنوان" 
                        Background="#4CAF50" Foreground="White"
                        Padding="10,5" Margin="0,10,0,0"
                        HorizontalAlignment="Left"
                        Click="AddIP_Click"/>
            </Grid>
        </GroupBox>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" 
                   HorizontalAlignment="Right">
            <Button x:Name="SaveButton" Content="💾 حفظ وإغلاق" 
                    Background="#4CAF50" Foreground="White"
                    Padding="15,8" Margin="10,0"
                    Click="Save_Click" IsDefault="True"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" 
                    Background="#757575" Foreground="White"
                    Padding="15,8" Margin="10,0"
                    Click="Cancel_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
