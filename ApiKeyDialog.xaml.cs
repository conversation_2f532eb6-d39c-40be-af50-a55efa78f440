using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Navigation;
using OpenAI;
using OpenAI.Chat;

namespace InzoIB_Simple
{
    public partial class ApiKeyDialog : Window
    {
        private readonly string apiKeyFilePath;
        public string ApiKey { get; private set; }
        public bool IsApiKeyValid { get; private set; }

        public ApiKeyDialog()
        {
            InitializeComponent();
            
            // تحديد مسار ملف حفظ API key
            var executablePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
            var executableDirectory = Path.GetDirectoryName(executablePath);
            apiKeyFilePath = Path.Combine(executableDirectory, "Data", "api_key.dat");
            
            LoadSavedApiKey();
            UpdateStatus();
        }

        private void LoadSavedApiKey()
        {
            try
            {
                if (File.Exists(apiKeyFilePath))
                {
                    var encryptedKey = File.ReadAllText(apiKeyFilePath);
                    ApiKey = DecryptString(encryptedKey);
                    ApiKeyTextBox.Text = MaskApiKey(ApiKey);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل API key: {ex.Message}");
            }
        }

        private void UpdateStatus()
        {
            if (string.IsNullOrEmpty(ApiKey) || ApiKey == "YOUR_OPENAI_API_KEY_HERE")
            {
                StatusText.Text = "❌ لم يتم تعيين مفتاح API";
                StatusBorder.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0xEB, 0xEE));
            }
            else
            {
                StatusText.Text = IsApiKeyValid ? 
                    "✅ مفتاح API محفوظ ومُختبر" : 
                    "⚠️ مفتاح API محفوظ لكن لم يتم اختباره";
                StatusBorder.Background = IsApiKeyValid ?
                    new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xE8, 0xF5, 0xE8)) :
                    new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0xF8, 0xE1));
            }
        }

        private string MaskApiKey(string apiKey)
        {
            if (string.IsNullOrEmpty(apiKey) || apiKey.Length < 10)
                return apiKey;
            
            return apiKey.Substring(0, 7) + "..." + apiKey.Substring(apiKey.Length - 4);
        }

        private async void TestButton_Click(object sender, RoutedEventArgs e)
        {
            var testKey = ApiKeyTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(testKey))
            {
                MessageBox.Show("يرجى إدخال مفتاح API أولاً", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // إذا كان النص مقنع، استخدم المفتاح الأصلي
            if (testKey.Contains("..."))
            {
                testKey = ApiKey;
            }

            TestButton.Content = "🔄 جاري الاختبار...";
            TestButton.IsEnabled = false;

            try
            {
                var client = new OpenAIClient(testKey);
                var chatClient = client.GetChatClient("gpt-3.5-turbo");
                
                var messages = new List<ChatMessage>
                {
                    ChatMessage.CreateSystemMessage("You are a helpful assistant."),
                    ChatMessage.CreateUserMessage("Hello, this is a test message.")
                };

                var options = new ChatCompletionOptions
                {
                    Temperature = 0.1f
                };

                var response = await chatClient.CompleteChatAsync(messages, options);
                
                IsApiKeyValid = true;
                ApiKey = testKey;
                
                MessageBox.Show("✅ تم اختبار مفتاح API بنجاح!\n\nالذكاء الاصطناعي جاهز للاستخدام.", 
                    "نجح الاختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                
                UpdateStatus();
            }
            catch (Exception ex)
            {
                IsApiKeyValid = false;
                MessageBox.Show($"❌ فشل في اختبار مفتاح API:\n\n{ex.Message}\n\nيرجى التحقق من صحة المفتاح والاتصال بالإنترنت.", 
                    "فشل الاختبار", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                TestButton.Content = "🧪 اختبار المفتاح";
                TestButton.IsEnabled = true;
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var keyToSave = ApiKeyTextBox.Text.Trim();
                
                if (string.IsNullOrEmpty(keyToSave))
                {
                    MessageBox.Show("يرجى إدخال مفتاح API أولاً", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إذا كان النص مقنع، استخدم المفتاح الأصلي
                if (keyToSave.Contains("..."))
                {
                    keyToSave = ApiKey;
                }

                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.GetDirectoryName(apiKeyFilePath);
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                // تشفير وحفظ المفتاح
                var encryptedKey = EncryptString(keyToSave);
                File.WriteAllText(apiKeyFilePath, encryptedKey);
                
                ApiKey = keyToSave;
                ApiKeyTextBox.Text = MaskApiKey(ApiKey);
                
                MessageBox.Show("✅ تم حفظ مفتاح API بنجاح!", "تم الحفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                UpdateStatus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في حفظ مفتاح API:\n\n{ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من حذف مفتاح API المحفوظ؟\n\nسيتم إيقاف الذكاء الاصطناعي.", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    if (File.Exists(apiKeyFilePath))
                    {
                        File.Delete(apiKeyFilePath);
                    }
                    
                    ApiKey = "";
                    IsApiKeyValid = false;
                    ApiKeyTextBox.Text = "";
                    
                    MessageBox.Show("✅ تم حذف مفتاح API بنجاح!", "تم الحذف", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    UpdateStatus();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ في حذف مفتاح API:\n\n{ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void Hyperlink_RequestNavigate(object sender, RequestNavigateEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = e.Uri.AbsoluteUri,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الرابط: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        // تشفير بسيط للمفتاح (لحماية أساسية)
        private string EncryptString(string text)
        {
            try
            {
                var data = Encoding.UTF8.GetBytes(text);
                var encrypted = ProtectedData.Protect(data, null, DataProtectionScope.CurrentUser);
                return Convert.ToBase64String(encrypted);
            }
            catch
            {
                return text; // في حالة فشل التشفير، احفظ النص كما هو
            }
        }

        private string DecryptString(string encryptedText)
        {
            try
            {
                var data = Convert.FromBase64String(encryptedText);
                var decrypted = ProtectedData.Unprotect(data, null, DataProtectionScope.CurrentUser);
                return Encoding.UTF8.GetString(decrypted);
            }
            catch
            {
                return encryptedText; // في حالة فشل فك التشفير، أرجع النص كما هو
            }
        }
    }
}
