🔧 حل مشاكل Firestore - دليل استكشاف الأخطاء
===============================================

❌ الأخطاء الشائعة وحلولها:

═══════════════════════════════════════════════════════════════

🔴 خطأ: "مفتاح API غير صالح أو لا يملك الصلاحيات المطلوبة"
═══════════════════════════════════════════════════════════════

🔍 الأسباب المحتملة:
• مفتاح API خاطئ أو منسوخ بشكل ناقص
• لم يتم تفعيل Firestore API
• قيود على مفتاح API
• انتهاء صلاحية المفتاح

✅ الحلول:
1️⃣ تحقق من نسخ المفتاح:
   • ارجع إلى Google Cloud Console
   • انسخ المفتاح كاملاً بدون مسافات
   • تأكد من عدم وجود أحرف إضافية

2️⃣ تفعيل Firestore API:
   • اذهب إلى APIs & Services > Library
   • ابحث عن "Cloud Firestore API"
   • تأكد من أنه "Enabled"

3️⃣ إنشاء مفتاح جديد:
   • اذهب إلى Credentials
   • احذف المفتاح القديم
   • أنشئ مفتاح جديد
   • قيده على Firestore API فقط

═══════════════════════════════════════════════════════════════

🔴 خطأ: "معرف المشروع غير صحيح أو المشروع غير موجود"
═══════════════════════════════════════════════════════════════

🔍 الأسباب المحتملة:
• خطأ في كتابة Project ID
• الخلط بين اسم المشروع و Project ID
• المشروع محذوف أو معطل

✅ الحلول:
1️⃣ التحقق من Project ID:
   • اذهب إلى Firebase Console
   • اضغط على ⚙️ Project Settings
   • انسخ "Project ID" (ليس Project name)

2️⃣ تأكد من صحة الكتابة:
   • Project ID يحتوي على أحرف صغيرة وأرقام وشرطات فقط
   • مثال صحيح: my-project-123
   • مثال خاطئ: My Project 123

3️⃣ تحقق من حالة المشروع:
   • تأكد من أن المشروع نشط
   • لم يتم حذفه أو تعطيله

═══════════════════════════════════════════════════════════════

🔴 خطأ: "خطأ في الشبكة - تحقق من اتصال الإنترنت"
═══════════════════════════════════════════════════════════════

🔍 الأسباب المحتملة:
• انقطاع الإنترنت
• حجب Firewall
• مشاكل في DNS
• بطء الاتصال

✅ الحلول:
1️⃣ تحقق من الإنترنت:
   • افتح موقع ويب للتأكد
   • جرب ping google.com

2️⃣ تحقق من Firewall:
   • تأكد من عدم حجب البرنامج
   • أضف استثناء للبرنامج

3️⃣ تغيير DNS:
   • جرب DNS عام مثل 8.8.8.8
   • أو 1.1.1.1

4️⃣ إعادة تشغيل الشبكة:
   • أعد تشغيل الراوتر
   • أعد الاتصال بالواي فاي

═══════════════════════════════════════════════════════════════

🔴 خطأ: "انتهت مهلة الاتصال"
═══════════════════════════════════════════════════════════════

🔍 الأسباب المحتملة:
• بطء الإنترنت
• مشاكل في خوادم Google
• حجم البيانات كبير

✅ الحلول:
1️⃣ تحسين الاتصال:
   • استخدم اتصال أسرع
   • أغلق التطبيقات الأخرى

2️⃣ إعادة المحاولة:
   • انتظر قليلاً وأعد المحاولة
   • جرب في وقت مختلف

3️⃣ تقليل البيانات:
   • احذف البيانات غير المهمة
   • جرب مع بيانات أقل

═══════════════════════════════════════════════════════════════

🔴 خطأ: "النص لا يظهر في حقول الإدخال"
═══════════════════════════════════════════════════════════════

✅ الحل (تم إصلاحه):
• تم حل هذه المشكلة في آخر تحديث
• النص الآن يظهر بوضوح
• الحقول بيضاء مع نص أسود
• إذا استمرت المشكلة، أعد تشغيل البرنامج

═══════════════════════════════════════════════════════════════

🔴 خطأ: "البيانات لا تتزامن"
═══════════════════════════════════════════════════════════════

🔍 الأسباب المحتملة:
• خطأ في الحفظ
• مشاكل في الصلاحيات
• تضارب في البيانات

✅ الحلول:
1️⃣ إعادة حفظ الإعدادات:
   • اذهب إلى إعدادات Firestore
   • أعد إدخال البيانات
   • احفظ مرة أخرى

2️⃣ تحقق من الصلاحيات:
   • تأكد من وضع "test mode"
   • راجع قواعد الأمان

3️⃣ مسح البيانات المحلية:
   • احذف ملف البيانات المحلي
   • أعد تشغيل البرنامج
   • سيتم تحميل البيانات من Firestore

═══════════════════════════════════════════════════════════════

🛠️ أدوات التشخيص:
═══════════════════════════════════════════════════════════════

🔍 فحص الاتصال:
1. افتح Command Prompt
2. اكتب: ping firestore.googleapis.com
3. يجب أن ترى ردود ناجحة

🔍 فحص DNS:
1. اكتب: nslookup firestore.googleapis.com
2. يجب أن ترى عنوان IP صحيح

🔍 فحص البرنامج:
1. شغل البرنامج
2. اذهب إلى إعدادات Firestore
3. اضغط "اختبار الاتصال"
4. راقب الرسائل

═══════════════════════════════════════════════════════════════

💡 نصائح الوقاية:
═══════════════════════════════════════════════════════════════

🔒 الأمان:
• لا تشارك API Key
• استخدم قواعد أمان مناسبة
• راجع الوصول بانتظام

💾 النسخ الاحتياطية:
• احتفظ بنسخة من الإعدادات
• صدر البيانات بانتظام
• استخدم عدة طرق للحفظ

🌐 الشبكة:
• استخدم اتصال مستقر
• تجنب الشبكات العامة للبيانات الحساسة
• راقب استهلاك البيانات

═══════════════════════════════════════════════════════════════

📞 طلب المساعدة:
═══════════════════════════════════════════════════════════════

إذا لم تحل المشكلة:
1. اكتب رسالة الخطأ بالضبط
2. اذكر الخطوات التي قمت بها
3. أرفق لقطة شاشة إن أمكن
4. اذكر نظام التشغيل المستخدم

معلومات مفيدة للدعم:
• إصدار البرنامج
• نوع الخطأ
• وقت حدوث الخطأ
• الخطوات المتبعة

═══════════════════════════════════════════════════════════════

✅ علامات النجاح:
═══════════════════════════════════════════════════════════════

🎯 كل شيء يعمل عندما ترى:
• "✅ تم الاتصال بنجاح!"
• البيانات تظهر في Firestore Console
• المزامنة تعمل بين الأجهزة
• لا توجد رسائل خطأ

🎊 مبروك! Firestore يعمل بنجاح!

تاريخ الدليل: 2025-01-21
آخر تحديث: تم حل جميع المشاكل المعروفة ✅
