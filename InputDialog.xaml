<Window x:Class="InzoIB_Simple.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة محتوى جديد" Height="400" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#161642" Padding="15,10" CornerRadius="5" Margin="0,0,0,20">
            <TextBlock x:Name="HeaderText" Text="إضافة محتوى جديد" 
                       FontSize="18" FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Title Input -->
        <StackPanel Grid.Row="1" Margin="0,0,0,15">
            <TextBlock Text="عنوان المحتوى (اختياري - اتركه فارغاً لعرض المحتوى فقط):" FontWeight="Bold" FontSize="14"
                       Foreground="#161642" Margin="0,0,0,5"/>
            <TextBox x:Name="TitleTextBox" FontSize="14" Padding="10,8"
                     BorderBrush="#161642" BorderThickness="2"
                     Text=""/>
        </StackPanel>

        <!-- Content Label -->
        <TextBlock Grid.Row="2" Text="نص المحتوى:" FontWeight="Bold" FontSize="14" 
                   Foreground="#161642" Margin="0,0,0,5"/>

        <!-- Content Input -->
        <Border Grid.Row="3" BorderBrush="#161642" BorderThickness="2" CornerRadius="3">
            <TextBox x:Name="ContentTextBox" 
                     TextWrapping="Wrap" AcceptsReturn="True" 
                     VerticalScrollBarVisibility="Auto"
                     FontSize="14" Padding="10"
                     BorderThickness="0"
                     Text="اكتب المحتوى الجديد هنا..."/>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="OkButton" Content="✅ موافق - إضافة" 
                    Background="#161642" Foreground="White" 
                    Padding="20,10" Margin="0,0,15,0" 
                    FontSize="14" FontWeight="Bold"
                    Click="OkButton_Click" IsDefault="True"
                    MinWidth="120"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء" 
                    Background="#f44336" Foreground="White" 
                    Padding="20,10" Margin="15,0,0,0" 
                    FontSize="14" FontWeight="Bold"
                    Click="CancelButton_Click" IsCancel="True"
                    MinWidth="120"/>
        </StackPanel>
    </Grid>
</Window>
