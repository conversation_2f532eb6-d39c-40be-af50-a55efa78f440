<Window x:Class="InzoIB_Simple.DividerInputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة شريط تقسيم" Height="300" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock x:Name="HeaderText" Text="إضافة شريط تقسيم جديد" 
                       FontSize="18" FontWeight="Bold" 
                       Foreground="#161642" HorizontalAlignment="Center"/>
            <TextBlock Text="يمكنك إضافة نص اختياري للشريط أو تركه فارغاً" 
                       FontSize="12" Foreground="Gray" 
                       HorizontalAlignment="Center" Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Divider Text Input -->
        <StackPanel Grid.Row="1" Margin="0,0,0,15">
            <TextBlock Text="نص الشريط (اختياري):" FontWeight="Bold" FontSize="14" 
                       Foreground="#161642" Margin="0,0,0,5"/>
            <TextBox x:Name="DividerTextBox" FontSize="14" Padding="10,8" 
                     BorderBrush="#161642" BorderThickness="2"
                     Text=""
                     MaxLength="50"
                     HorizontalAlignment="Stretch"/>
            <TextBlock Text="مثال: قسم جديد، فاصل، تصنيف..." 
                       FontSize="10" Foreground="Gray" 
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button x:Name="OkButton" Content="✅ إضافة الشريط"
                    Background="#4CAF50" Foreground="White"
                    Padding="15,8" Margin="10,0"
                    FontSize="14" FontWeight="Bold"
                    Click="OkButton_Click"/>
            <Button x:Name="CancelButton" Content="❌ إلغاء"
                    Background="#f44336" Foreground="White"
                    Padding="15,8" Margin="10,0"
                    FontSize="14" FontWeight="Bold"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
