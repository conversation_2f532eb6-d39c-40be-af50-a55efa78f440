using System;
using System.Windows;
using System.Windows.Threading;

namespace InzoIB_Simple
{
    public partial class ToastWindow : Window
    {
        private DispatcherTimer timer;

        public ToastWindow(string message)
        {
            InitializeComponent();
            MessageText.Text = message;
            
            // تحديد موقع النافذة في أسفل يمين الشاشة
            this.Left = SystemParameters.PrimaryScreenWidth - this.Width - 20;
            this.Top = SystemParameters.PrimaryScreenHeight - this.Height - 100;
            
            // إعداد المؤقت للإغلاق التلقائي
            timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1.5);
            timer.Tick += Timer_Tick;
            timer.Start();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            timer.Stop();
            this.Close();
        }

        public static void Show(string message)
        {
            var toast = new ToastWindow(message);
            toast.Show();
        }
    }
}
