using System.Windows;

namespace InzoIB_Simple
{
    public partial class DividerInputDialog : Window
    {
        public string DividerText { get; private set; }

        public DividerInputDialog()
        {
            InitializeComponent();
            
            // التركيز على صندوق النص
            DividerTextBox.Focus();
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            // حفظ نص الشريط (يمكن أن يكون فارغاً)
            DividerText = DividerTextBox.Text.Trim();

            // إغلاق النافذة بنجاح
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة بدون حفظ
            DialogResult = false;
            Close();
        }
    }
}
