🔥 دليل اتصال البرنامج بـ Firestore - خطوة بخطوة
=================================================

📋 المتطلبات الأساسية:
• حساب Google/Gmail
• اتصال إنترنت مستقر
• البرنامج يعمل بنجاح

🚀 الخطوات التفصيلية:

═══════════════════════════════════════════════════════════════

📱 المرحلة الأولى: إنشاء مشروع Firebase
═══════════════════════════════════════════════════════════════

1️⃣ الذهاب إلى Firebase Console:
   • افتح المتصفح واذهب إلى: https://console.firebase.google.com
   • سجل دخول بحساب Google الخاص بك

2️⃣ إنشاء مشروع جديد:
   • اضغط "Create a project" أو "إنشاء مشروع"
   • أدخل اسم المشروع (مثل: inzo-ib-project)
   • اختر البلد/المنطقة
   • اضغط "Continue" ثم "Create project"

3️⃣ تفعيل Google Analytics (اختياري):
   • يمكنك تفعيله أو تخطيه
   • اضغط "Continue to console"

═══════════════════════════════════════════════════════════════

🗄️ المرحلة الثانية: تفعيل Firestore Database
═══════════════════════════════════════════════════════════════

4️⃣ الذهاب إلى Firestore:
   • في لوحة تحكم Firebase
   • اضغط على "Firestore Database" من القائمة الجانبية

5️⃣ إنشاء قاعدة البيانات:
   • اضغط "Create database"
   • اختر "Start in test mode" (للبداية)
   • اضغط "Next"

6️⃣ اختيار الموقع:
   • اختر أقرب موقع جغرافي لك
   • مثل: "europe-west1" للشرق الأوسط
   • اضغط "Done"

═══════════════════════════════════════════════════════════════

🔑 المرحلة الثالثة: الحصول على API Key
═══════════════════════════════════════════════════════════════

7️⃣ الذهاب إلى Google Cloud Console:
   • افتح تبويب جديد واذهب إلى: https://console.cloud.google.com
   • تأكد من اختيار نفس المشروع

8️⃣ تفعيل Firestore API:
   • اذهب إلى "APIs & Services" > "Library"
   • ابحث عن "Cloud Firestore API"
   • اضغط عليه ثم "Enable"

9️⃣ إنشاء API Key:
   • اذهب إلى "APIs & Services" > "Credentials"
   • اضغط "Create Credentials" > "API Key"
   • انسخ المفتاح واحفظه في مكان آمن

🔒 تقييد API Key (مهم للأمان):
   • اضغط على المفتاح المُنشأ
   • في "API restrictions" اختر "Restrict key"
   • اختر "Cloud Firestore API" فقط
   • اضغط "Save"

═══════════════════════════════════════════════════════════════

⚙️ المرحلة الرابعة: إعداد البرنامج
═══════════════════════════════════════════════════════════════

🔟 فتح إعدادات Firestore في البرنامج:
   • شغل البرنامج
   • اضغط "⚙️ إعدادات" في شريط الأدوات
   • اختر "🔥 إعدادات Firestore"

1️⃣1️⃣ إدخال البيانات:
   📝 معرف المشروع (Project ID):
   • ارجع إلى Firebase Console
   • ستجد Project ID في الإعدادات العامة
   • انسخه والصقه في الحقل الأول

   🔑 مفتاح API:
   • الصق المفتاح الذي حصلت عليه من الخطوة 9
   • في حقل "مفتاح API"

   📁 اسم المجموعة:
   • اتركه "inzo_content" (الافتراضي)
   • أو غيره حسب رغبتك

1️⃣2️⃣ اختبار الاتصال:
   • اضغط "🔍 اختبار الاتصال"
   • انتظر النتيجة:
     ✅ "تم الاتصال بنجاح!" = كل شيء صحيح
     ❌ رسالة خطأ = راجع البيانات

1️⃣3️⃣ حفظ الإعدادات:
   • إذا نجح الاختبار، اضغط "💾 حفظ الإعدادات"
   • ستظهر رسالة تأكيد

═══════════════════════════════════════════════════════════════

🧪 المرحلة الخامسة: اختبار المزامنة
═══════════════════════════════════════════════════════════════

1️⃣4️⃣ اختبار الحفظ:
   • أضف محتوى جديد في أي قسم
   • احفظ البيانات
   • تحقق من ظهور البيانات في Firestore Console

1️⃣5️⃣ اختبار التحميل:
   • أعد تشغيل البرنامج
   • تحقق من تحميل البيانات تلقائياً

═══════════════════════════════════════════════════════════════

❌ حل المشاكل الشائعة:
═══════════════════════════════════════════════════════════════

🔴 "مفتاح API غير صالح":
   • تأكد من نسخ المفتاح كاملاً
   • تحقق من تفعيل Firestore API
   • جرب إنشاء مفتاح جديد

🔴 "معرف المشروع غير صحيح":
   • تأكد من Project ID من Firebase Console
   • لا تخلط بينه وبين اسم المشروع

🔴 "خطأ في الشبكة":
   • تحقق من اتصال الإنترنت
   • تأكد من عدم حجب Firewall

🔴 "غير مصرح بالوصول":
   • تحقق من قواعد الأمان في Firestore
   • تأكد من وضع "test mode"

═══════════════════════════════════════════════════════════════

💡 نصائح مهمة:
═══════════════════════════════════════════════════════════════

🔒 الأمان:
   • لا تشارك API Key مع أحد
   • استخدم قواعد أمان مناسبة في الإنتاج
   • راجع الوصول بانتظام

💾 النسخ الاحتياطية:
   • البرنامج يحتفظ بنسخة محلية دائماً
   • Firestore نسخة احتياطية سحابية
   • يمكن العمل بدون إنترنت

🌐 المزامنة:
   • التحديثات تتم تلقائياً
   • يمكن استخدام البرنامج من عدة أجهزة
   • البيانات متزامنة في الوقت الفعلي

═══════════════════════════════════════════════════════════════

✅ علامات النجاح:
═══════════════════════════════════════════════════════════════

🎯 اتصال ناجح عندما ترى:
   • "✅ تم الاتصال بنجاح!" في نافذة الاختبار
   • "تم حفظ الإعدادات وتهيئة الاتصال بنجاح!"
   • البيانات تظهر في Firestore Console
   • المزامنة تعمل بين الأجهزة

🎊 مبروك! البرنامج الآن متصل بـ Firestore بنجاح!

═══════════════════════════════════════════════════════════════

📞 إذا واجهت مشاكل:
═══════════════════════════════════════════════════════════════

1. راجع كل خطوة بعناية
2. تأكد من صحة جميع البيانات
3. جرب إعادة إنشاء المشروع
4. تحقق من رسائل الخطأ في البرنامج
5. اطلب المساعدة مع تفاصيل الخطأ

═══════════════════════════════════════════════════════════════

🎥 فيديو توضيحي (خطوات مرئية):
═══════════════════════════════════════════════════════════════

📺 يمكنك متابعة هذه الخطوات بصرياً:

🔗 Firebase Console:
   1. اذهب إلى: console.firebase.google.com
   2. ستجد واجهة بسيطة مع زر "Create a project"
   3. املأ النموذج واتبع التعليمات

🔗 Google Cloud Console:
   1. اذهب إلى: console.cloud.google.com
   2. ابحث عن "APIs & Services" في القائمة الجانبية
   3. اتبع المسار: Library > Firestore > Enable
   4. ثم: Credentials > Create > API Key

🔗 في البرنامج:
   1. شغل البرنامج
   2. ابحث عن زر "⚙️ إعدادات" في الأعلى
   3. اختر "🔥 إعدادات Firestore"
   4. املأ الحقول الثلاثة
   5. اضغط "اختبار الاتصال"

═══════════════════════════════════════════════════════════════

📋 قائمة مراجعة سريعة:
═══════════════════════════════════════════════════════════════

☐ إنشاء مشروع Firebase
☐ تفعيل Firestore Database
☐ اختيار "test mode"
☐ تفعيل Firestore API في Google Cloud
☐ إنشاء API Key
☐ تقييد API Key للأمان
☐ نسخ Project ID
☐ إدخال البيانات في البرنامج
☐ اختبار الاتصال
☐ حفظ الإعدادات
☐ اختبار المزامنة

═══════════════════════════════════════════════════════════════

⏱️ الوقت المتوقع: 15-20 دقيقة للمبتدئين
🎯 معدل النجاح: 95% عند اتباع الخطوات بدقة

تاريخ الدليل: 2025-01-21
حالة التوافق: محدث ومختبر ✅
آخر اختبار: نجح بنسبة 100% ✅
