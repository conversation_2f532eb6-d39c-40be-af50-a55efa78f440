﻿#pragma checksum "..\..\..\FileAttachmentWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E160C8F2653888D205D3D2E757C0D504BF8A0D48"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InzoIB_Simple {
    
    
    /// <summary>
    /// FileAttachmentWindow
    /// </summary>
    public partial class FileAttachmentWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 18 "..\..\..\FileAttachmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderText;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\FileAttachmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ContentDisplayArea;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\FileAttachmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFileButton;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\FileAttachmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyAttachedFileButton;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\FileAttachmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveFileButton;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\FileAttachmentWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InzoIB_v7.4_Simple;V7.4.0.0;component/fileattachmentwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\FileAttachmentWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ContentDisplayArea = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.AddFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\FileAttachmentWindow.xaml"
            this.AddFileButton.Click += new System.Windows.RoutedEventHandler(this.AddFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CopyAttachedFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\FileAttachmentWindow.xaml"
            this.CopyAttachedFileButton.Click += new System.Windows.RoutedEventHandler(this.CopyAttachedFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RemoveFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\FileAttachmentWindow.xaml"
            this.RemoveFileButton.Click += new System.Windows.RoutedEventHandler(this.RemoveFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\FileAttachmentWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

