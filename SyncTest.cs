using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Diagnostics;

namespace InzoIB_Simple
{
    /// <summary>
    /// فئة اختبار نظام المزامنة الشامل
    /// </summary>
    public class SyncTest
    {
        /// <summary>
        /// اختبار المزامنة الشاملة
        /// </summary>
        /// <param name="universalSyncManager">مدير المزامنة الشامل</param>
        /// <returns>نتيجة الاختبار</returns>
        public static async Task<bool> TestUniversalSync(UniversalSyncManager universalSyncManager)
        {
            try
            {
                Debug.WriteLine("🧪 بدء اختبار نظام المزامنة الشامل...");

                // اختبار 1: إنشاء عنصر محتوى تجريبي
                var testItems = new List<ContentItem>
                {
                    new ContentItem
                    {
                        Id = Guid.NewGuid().ToString(),
                        Section = "اختبار",
                        Title = "عنصر اختبار المزامنة",
                        Content = "هذا محتوى تجريبي لاختبار نظام المزامنة الشامل",
                        CreatedDate = DateTime.Now,
                        Timestamp = DateTime.Now
                    }
                };

                Debug.WriteLine("✅ تم إنشاء عنصر اختبار");

                // اختبار 2: مزامنة البيانات
                await universalSyncManager.SyncDataUniversallyAsync(testItems, "TEST");
                Debug.WriteLine("✅ تم اختبار مزامنة البيانات");

                // اختبار 3: مزامنة الإعدادات
                await universalSyncManager.SyncSettingsUniversallyAsync("test_setting", "test_value");
                Debug.WriteLine("✅ تم اختبار مزامنة الإعدادات");

                // اختبار 4: الحصول على حالة المزامنة
                var status = await universalSyncManager.GetSyncStatusAsync();
                Debug.WriteLine("✅ تم اختبار الحصول على حالة المزامنة");
                Debug.WriteLine($"📊 حالة المزامنة:\n{status}");

                Debug.WriteLine("🎉 تم إكمال جميع اختبارات المزامنة بنجاح!");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ فشل اختبار المزامنة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار سريع للمزامنة
        /// </summary>
        /// <param name="universalSyncManager">مدير المزامنة الشامل</param>
        /// <returns>نتيجة الاختبار</returns>
        public static async Task<string> QuickSyncTest(UniversalSyncManager universalSyncManager)
        {
            try
            {
                var startTime = DateTime.Now;
                
                // اختبار سريع للمزامنة
                var testItem = new ContentItem
                {
                    Id = Guid.NewGuid().ToString(),
                    Section = "اختبار سريع",
                    Title = "اختبار سريع",
                    Content = $"اختبار سريع في {DateTime.Now:HH:mm:ss}",
                    CreatedDate = DateTime.Now,
                    Timestamp = DateTime.Now
                };

                await universalSyncManager.SyncDataUniversallyAsync(new List<ContentItem> { testItem }, "QUICK_TEST");
                
                var duration = DateTime.Now - startTime;
                return $"✅ اختبار سريع مكتمل في {duration.TotalMilliseconds:F0} مللي ثانية";
            }
            catch (Exception ex)
            {
                return $"❌ فشل الاختبار السريع: {ex.Message}";
            }
        }
    }
}
