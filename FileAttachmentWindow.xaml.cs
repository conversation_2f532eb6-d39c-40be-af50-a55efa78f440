using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.Win32;

namespace InzoIB_Simple
{
    public partial class FileAttachmentWindow : Window
    {
        public List<string> AttachedFiles { get; private set; }
        private string contentTitle;

        public FileAttachmentWindow(string title, List<string> existingFiles = null)
        {
            InitializeComponent();
            contentTitle = title;
            AttachedFiles = existingFiles ?? new List<string>();
            
            HeaderText.Text = $"إدارة الملف المرفق - {title}";
            LoadFiles();
        }

        private void LoadFiles()
        {
            ContentDisplayArea.Children.Clear();

            if (AttachedFiles.Count == 0)
            {
                // لا نعرض أي رسالة - النافذة تبقى فارغة
                return;
            }

            var filePath = AttachedFiles.First(); // ملف واحد فقط

            if (IsImageFile(filePath) && File.Exists(filePath))
            {
                // عرض الصورة بشكل كامل
                DisplayFullImage(filePath);
            }
            else
            {
                // عرض معلومات الملف
                DisplayFileInfo(filePath);
            }
        }

        private void DisplayFullImage(string filePath)
        {
            try
            {
                var image = new Image
                {
                    Stretch = Stretch.Uniform,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };

                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(filePath);
                bitmap.EndInit();

                image.Source = bitmap;
                ContentDisplayArea.Children.Add(image);

                // إضافة معلومات الصورة في الأسفل
                var infoPanel = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 10, 0, 0)
                };

                var fileInfo = new FileInfo(filePath);
                var infoText = new TextBlock
                {
                    Text = $"📁 {Path.GetFileName(filePath)}\n📏 {GetFileSize(filePath)}\n📅 {fileInfo.LastWriteTime:yyyy/MM/dd HH:mm}",
                    FontSize = 12,
                    Foreground = Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center
                };

                infoPanel.Children.Add(infoText);
                ContentDisplayArea.Children.Add(infoPanel);
            }
            catch (Exception ex)
            {
                var errorText = new TextBlock
                {
                    Text = $"❌ خطأ في تحميل الصورة:\n{ex.Message}",
                    FontSize = 14,
                    Foreground = Brushes.Red,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    TextAlignment = TextAlignment.Center
                };
                ContentDisplayArea.Children.Add(errorText);
            }
        }

        private void DisplayFileInfo(string filePath)
        {
            var mainPanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            // أيقونة الملف الكبيرة
            var fileIcon = new TextBlock
            {
                Text = GetFileIcon(filePath),
                FontSize = 80,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };

            // اسم الملف
            var fileName = new TextBlock
            {
                Text = Path.GetFileName(filePath),
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10),
                TextWrapping = TextWrapping.Wrap
            };

            // معلومات الملف
            var fileInfo = new FileInfo(filePath);
            var infoText = new TextBlock
            {
                Text = $"📏 الحجم: {GetFileSize(filePath)}\n📅 تاريخ التعديل: {fileInfo.LastWriteTime:yyyy/MM/dd HH:mm}\n📂 المسار: {filePath}",
                FontSize = 14,
                Foreground = Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 20)
            };

            // أزرار الإدارة
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var openButton = new Button
            {
                Content = "📂 فتح الملف",
                Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                Foreground = Brushes.White,
                Padding = new Thickness(15, 8, 15, 8),
                Margin = new Thickness(5, 5, 5, 5),
                FontSize = 14,
                FontWeight = FontWeights.Bold
            };
            openButton.Click += (s, e) => OpenFile(filePath);

            var copyButton = new Button
            {
                Content = "📋 نسخ الملف",
                Background = new SolidColorBrush(Color.FromRgb(0x4C, 0xAF, 0x50)),
                Foreground = Brushes.White,
                Padding = new Thickness(15, 8, 15, 8),
                Margin = new Thickness(5, 5, 5, 5),
                FontSize = 14,
                FontWeight = FontWeights.Bold
            };
            copyButton.Click += (s, e) => CopyFile(filePath);

            buttonPanel.Children.Add(openButton);
            buttonPanel.Children.Add(copyButton);

            mainPanel.Children.Add(fileIcon);
            mainPanel.Children.Add(fileName);
            mainPanel.Children.Add(infoText);
            mainPanel.Children.Add(buttonPanel);

            ContentDisplayArea.Children.Add(mainPanel);
        }

        private void OpenFile(string filePath)
        {
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                try
                {
                    Process.Start(new ProcessStartInfo(filePath) { UseShellExecute = true });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"لا يمكن فتح الملف:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("الملف غير موجود أو تم حذفه", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void CopyFile(string filePath)
        {
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                try
                {
                    var fileList = new System.Collections.Specialized.StringCollection();
                    fileList.Add(filePath);
                    Clipboard.SetFileDropList(fileList);
                    ToastWindow.Show($"تم نسخ الملف: {Path.GetFileName(filePath)}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"لا يمكن نسخ الملف:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("الملف غير موجود أو تم حذفه", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }



        private string GetFileIcon(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return extension switch
            {
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".ppt" or ".pptx" => "📋",
                ".txt" => "📃",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "🖼️",
                ".mp4" or ".avi" or ".mkv" or ".mov" => "🎥",
                ".mp3" or ".wav" or ".flac" => "🎵",
                ".zip" or ".rar" or ".7z" => "📦",
                _ => "📁"
            };
        }

        private string GetFileSize(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    var sizeInBytes = fileInfo.Length;
                    
                    if (sizeInBytes < 1024)
                        return $"{sizeInBytes} بايت";
                    else if (sizeInBytes < 1024 * 1024)
                        return $"{sizeInBytes / 1024:F1} كيلوبايت";
                    else if (sizeInBytes < 1024 * 1024 * 1024)
                        return $"{sizeInBytes / (1024 * 1024):F1} ميجابايت";
                    else
                        return $"{sizeInBytes / (1024 * 1024 * 1024):F1} جيجابايت";
                }
                else
                {
                    return "غير موجود";
                }
            }
            catch
            {
                return "غير معروف";
            }
        }

        private void AddFileButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من وجود ملف مرفق بالفعل
            if (AttachedFiles.Count > 0)
            {
                var result = MessageBox.Show("يوجد ملف مرفق بالفعل. هل تريد استبداله بملف جديد؟",
                    "استبدال الملف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                    return;
            }

            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر ملف للإرفاق",
                Filter = "جميع الملفات (*.*)|*.*|مستندات (*.pdf;*.doc;*.docx)|*.pdf;*.doc;*.docx|صور (*.jpg;*.png;*.gif)|*.jpg;*.png;*.gif|فيديو (*.mp4;*.avi;*.mkv)|*.mp4;*.avi;*.mkv|صوت (*.mp3;*.wav)|*.mp3;*.wav",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                var fileName = openFileDialog.FileName;

                // مسح الملفات السابقة وإضافة الملف الجديد
                AttachedFiles.Clear();
                AttachedFiles.Add(fileName);
                LoadFiles();
                ToastWindow.Show($"تم إرفاق الملف: {Path.GetFileName(fileName)}");
            }
        }

        private void CopyAttachedFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (AttachedFiles.Count == 0)
            {
                MessageBox.Show("لا يوجد ملف مرفق لنسخه!", "لا يوجد ملف", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var filePath = AttachedFiles.First();

            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                try
                {
                    var fileList = new System.Collections.Specialized.StringCollection();
                    fileList.Add(filePath);
                    Clipboard.SetFileDropList(fileList);
                    ToastWindow.Show($"تم نسخ الملف المرفق: {Path.GetFileName(filePath)}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"لا يمكن نسخ الملف:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("الملف المرفق غير موجود أو تم حذفه", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }



        private bool IsImageFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return extension == ".jpg" || extension == ".jpeg" || extension == ".png" ||
                   extension == ".gif" || extension == ".bmp" || extension == ".tiff";
        }



        private void RemoveFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (AttachedFiles.Count == 0)
            {
                MessageBox.Show("لا يوجد ملف مرفق لإزالته!", "لا يوجد ملف", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var filePath = AttachedFiles.First();
            var result = MessageBox.Show($"هل أنت متأكد من إزالة الملف:\n{Path.GetFileName(filePath)}؟",
                "تأكيد الإزالة", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                AttachedFiles.Clear();
                LoadFiles();
                ToastWindow.Show($"تم إزالة الملف: {Path.GetFileName(filePath)}");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }
    }
}
