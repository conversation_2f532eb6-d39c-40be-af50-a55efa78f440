using System;
using System.Windows;

namespace InzoIB_Simple
{
    public partial class SimpleApiKeyDialog : Window
    {
        public string ApiKey { get; private set; }

        public SimpleApiKeyDialog()
        {
            InitializeComponent();
            
            // التركيز على صندوق النص
            Loaded += (s, e) => ApiKeyTextBox.Focus();
        }

        public SimpleApiKeyDialog(string existingApiKey) : this()
        {
            if (!string.IsNullOrEmpty(existingApiKey))
            {
                ApiKeyTextBox.Text = existingApiKey;
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            var apiKey = ApiKeyTextBox.Text?.Trim();
            
            if (string.IsNullOrEmpty(apiKey))
            {
                MessageBox.Show("يرجى إدخال مفتاح API", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                ApiKeyTextBox.Focus();
                return;
            }

            if (apiKey.Length < 10)
            {
                MessageBox.Show("مفتاح API قصير جداً. يرجى التأكد من المفتاح الصحيح", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                ApiKeyTextBox.Focus();
                return;
            }

            ApiKey = apiKey;
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ApiKeyTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                OkButton_Click(sender, e);
            }
            else if (e.Key == System.Windows.Input.Key.Escape)
            {
                CancelButton_Click(sender, e);
            }
        }
    }
}
