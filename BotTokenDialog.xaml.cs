using System;
using System.Threading.Tasks;
using System.Windows;
using Telegram.Bot;

namespace InzoIB_Simple
{
    public partial class BotTokenDialog : Window
    {
        public string BotToken { get; private set; }

        public BotTokenDialog(string currentToken = "")
        {
            InitializeComponent();
            
            // إذا كان هناك توكن حالي، اعرضه مقنعاً
            if (!string.IsNullOrEmpty(currentToken))
            {
                TokenTextBox.Text = MaskToken(currentToken);
                BotToken = currentToken;
            }
            
            // التركيز على حقل النص
            TokenTextBox.Focus();
        }

        private string MaskToken(string token)
        {
            if (string.IsNullOrEmpty(token) || token.Length < 10)
                return token;
            
            var parts = token.Split(':');
            if (parts.Length == 2)
            {
                return $"{parts[0]}:{"*".PadLeft(parts[1].Length, '*')}";
            }
            
            return token.Substring(0, 10) + "*".PadLeft(token.Length - 10, '*');
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var tokenToSave = TokenTextBox.Text.Trim();
                
                if (string.IsNullOrEmpty(tokenToSave))
                {
                    MessageBox.Show("يرجى إدخال توكن البوت أولاً", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إذا كان النص مقنع، استخدم التوكن الأصلي
                if (tokenToSave.Contains("*"))
                {
                    tokenToSave = BotToken;
                }

                // التحقق من صحة التوكن
                if (!IsValidTokenFormat(tokenToSave))
                {
                    MessageBox.Show("تنسيق التوكن غير صحيح. يجب أن يكون بالشكل:\nXXXXXXXXX:XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX", 
                        "خطأ في التوكن", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                BotToken = tokenToSave;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في حفظ التوكن:\n\n{ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TestButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var tokenToTest = TokenTextBox.Text.Trim();
                
                if (string.IsNullOrEmpty(tokenToTest))
                {
                    MessageBox.Show("يرجى إدخال توكن البوت أولاً", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إذا كان النص مقنع، استخدم التوكن الأصلي
                if (tokenToTest.Contains("*"))
                {
                    tokenToTest = BotToken;
                }

                if (!IsValidTokenFormat(tokenToTest))
                {
                    MessageBox.Show("تنسيق التوكن غير صحيح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تعطيل الأزرار أثناء الاختبار
                TestButton.IsEnabled = false;
                TestButton.Content = "🔄 جاري الاختبار...";

                try
                {
                    var testBot = new TelegramBotClient(tokenToTest);
                    var me = await testBot.GetMeAsync();
                    
                    MessageBox.Show($"✅ نجح اختبار البوت!\n\n🤖 اسم البوت: {me.FirstName}\n👤 اسم المستخدم: @{me.Username}\n🆔 معرف البوت: {me.Id}", 
                        "نجح الاختبار", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // حفظ التوكن إذا نجح الاختبار
                    BotToken = tokenToTest;
                }
                catch (Exception testEx)
                {
                    string errorMessage = "❌ فشل اختبار البوت!\n\n";
                    
                    if (testEx.Message.Contains("Unauthorized"))
                    {
                        errorMessage += "التوكن غير صحيح أو منتهي الصلاحية.";
                    }
                    else if (testEx.Message.Contains("Not Found"))
                    {
                        errorMessage += "البوت غير موجود أو محذوف.";
                    }
                    else if (testEx.Message.Contains("network") || testEx.Message.Contains("timeout"))
                    {
                        errorMessage += "مشكلة في الاتصال بالإنترنت.";
                    }
                    else
                    {
                        errorMessage += $"خطأ: {testEx.Message}";
                    }
                    
                    errorMessage += "\n\n💡 تأكد من:\n• صحة التوكن\n• اتصال الإنترنت\n• أن البوت نشط";
                    
                    MessageBox.Show(errorMessage, "فشل الاختبار", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في اختبار التوكن:\n\n{ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تفعيل الزر
                TestButton.IsEnabled = true;
                TestButton.Content = "🧪 اختبار";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool IsValidTokenFormat(string token)
        {
            if (string.IsNullOrEmpty(token))
                return false;
            
            var parts = token.Split(':');
            if (parts.Length != 2)
                return false;
            
            // الجزء الأول يجب أن يكون رقم (Bot ID)
            if (!long.TryParse(parts[0], out _))
                return false;
            
            // الجزء الثاني يجب أن يكون على الأقل 35 حرف
            if (parts[1].Length < 35)
                return false;
            
            return true;
        }

        private void TokenTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            // إذا كان النص مقنع، امحه للسماح بإدخال توكن جديد
            if (TokenTextBox.Text.Contains("*"))
            {
                TokenTextBox.Text = "";
            }
        }
    }
}
