<Window x:Class="InzoIB_Simple.TestTextWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="اختبار النصوص" Height="400" Width="500"
        Background="#FF2D2D30"
        WindowStartupLocation="CenterScreen">
    
    <Grid Margin="20">
        <StackPanel>
            <TextBlock Text="🧪 نافذة اختبار النصوص" 
                       FontSize="20" 
                       FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,20"/>
            
            <TextBlock Text="إذا كانت النصوص تظهر هنا، فالمشكلة في نافذة Firestore فقط:" 
                       FontSize="14" 
                       Foreground="White" 
                       Margin="0,0,0,10"/>
            
            <!-- اختبار 1: TextBox بسيط -->
            <TextBlock Text="1. TextBox بسيط:" FontSize="14" Foreground="White" Margin="0,10,0,5"/>
            <TextBox Name="SimpleTextBox"
                     Background="White"
                     Foreground="Black"
                     BorderBrush="Blue"
                     BorderThickness="2"
                     Height="45"
                     Padding="10,12,10,8"
                     VerticalContentAlignment="Center"
                     FontSize="14"
                     Text="اكتب هنا - يجب أن يظهر النص"/>
            
            <!-- اختبار 2: PasswordBox بسيط -->
            <TextBlock Text="2. PasswordBox بسيط:" FontSize="14" Foreground="White" Margin="0,10,0,5"/>
            <PasswordBox Name="SimplePasswordBox"
                         Background="White"
                         Foreground="Black"
                         BorderBrush="Blue"
                         BorderThickness="2"
                         Height="45"
                         Padding="10,12,10,8"
                         VerticalContentAlignment="Center"
                         FontSize="14"/>
            
            <!-- اختبار 3: TextBox مع نمط -->
            <TextBlock Text="3. TextBox مع نمط:" FontSize="14" Foreground="White" Margin="0,10,0,5"/>
            <TextBox Name="StyledTextBox"
                     Height="45"
                     Padding="10,12,10,8"
                     VerticalContentAlignment="Center"
                     Text="نص تجريبي">
                <TextBox.Style>
                    <Style TargetType="TextBox">
                        <Setter Property="Background" Value="Yellow"/>
                        <Setter Property="Foreground" Value="Red"/>
                        <Setter Property="BorderBrush" Value="Green"/>
                        <Setter Property="BorderThickness" Value="3"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                    </Style>
                </TextBox.Style>
            </TextBox>
            
            <!-- زر الاختبار -->
            <Button Name="TestButton" 
                    Content="🔍 اختبار النصوص" 
                    Background="#FF0078D4" 
                    Foreground="White" 
                    BorderThickness="0" 
                    Height="35" 
                    Margin="0,20,0,10"
                    Click="TestButton_Click"/>
            
            <!-- نتيجة الاختبار -->
            <TextBlock Name="ResultTextBlock" 
                       FontSize="14" 
                       Foreground="Yellow" 
                       HorizontalAlignment="Center" 
                       Margin="0,10,0,0"/>
        </StackPanel>
    </Grid>
</Window>
