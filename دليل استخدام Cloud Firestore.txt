🔥 دليل استخدام Cloud Firestore مع Inzo IB v7.4
===============================================

📋 نظرة عامة:
تم تطوير البرنامج ليدعم Cloud Firestore من Google كقاعدة بيانات سحابية لحفظ ومزامنة البيانات عبر الأجهزة المختلفة.

🎯 المميزات الجديدة:
✅ حفظ البيانات في Cloud Firestore
✅ مزامنة تلقائية عبر الأجهزة
✅ نسخ احتياطية محلية
✅ واجهة سهلة لإدارة الإعدادات
✅ معالجة شاملة للأخطاء
✅ تشفير معلومات الاتصال

🚀 خطوات الإعداد:

1️⃣ إنشاء مشروع Firebase:
   • اذهب إلى https://console.firebase.google.com
   • اضغط "Create a project" أو "إنشاء مشروع"
   • أدخل اسم المشروع (مثل: inzo-ib-data)
   • اتبع خطوات الإعداد

2️⃣ تفعيل Cloud Firestore:
   • في لوحة تحكم Firebase، اذهب إلى "Firestore Database"
   • اضغط "Create database"
   • اختر "Start in test mode" للبداية
   • اختر موقع قاعدة البيانات (يفضل أقرب منطقة)

3️⃣ الحصول على مفتاح API:
   • اذهب إلى Google Cloud Console: https://console.cloud.google.com
   • اختر نفس المشروع
   • اذهب إلى "APIs & Services" > "Credentials"
   • اضغط "Create Credentials" > "API Key"
   • انسخ المفتاح واحفظه في مكان آمن

4️⃣ تكوين البرنامج:
   • شغل برنامج Inzo IB
   • اضغط على زر "⚙️ إعدادات" في شريط الأدوات
   • اختر "🔥 إعدادات Firestore"
   • أدخل المعلومات التالية:
     - معرف المشروع (Project ID)
     - مفتاح API
     - اسم المجموعة (افتراضي: inzo_content)

5️⃣ اختبار الاتصال:
   • اضغط "🔍 اختبار الاتصال"
   • انتظر رسالة النجاح
   • اضغط "💾 حفظ الإعدادات"

🔧 كيفية الاستخدام:

📤 حفظ البيانات:
• البرنامج يحفظ البيانات تلقائياً في Firestore عند:
  - إضافة محتوى جديد
  - تعديل محتوى موجود
  - حذف محتوى
  - استلام رسائل تيليجرام

📥 تحميل البيانات:
• البرنامج يحمل البيانات تلقائياً من Firestore عند:
  - بدء تشغيل البرنامج
  - تفعيل إعدادات Firestore
  - حدوث تحديث من جهاز آخر

🔄 المزامنة بين الأجهزة:
• شغل البرنامج على أجهزة متعددة
• استخدم نفس معلومات Firestore على جميع الأجهزة
• التغييرات ستظهر تلقائياً على جميع الأجهزة

💾 النسخ الاحتياطية:
• البرنامج يحتفظ بنسخة احتياطية محلية دائماً
• في حالة عدم توفر الإنترنت، يعمل البرنامج محلياً
• عند عودة الاتصال، يتم رفع التغييرات تلقائياً

⚠️ ملاحظات مهمة:

🔒 الأمان:
• مفتاح API يُحفظ مشفراً محلياً
• لا تشارك مفتاح API مع أحد
• استخدم قواعد الأمان في Firestore

💰 التكلفة:
• Firestore مجاني حتى حد معين
• للاستخدام الشخصي عادة لا توجد تكلفة
• راجع أسعار Firebase للتفاصيل

🌐 الاتصال:
• يتطلب اتصال إنترنت للمزامنة
• يعمل محلياً بدون إنترنت
• يزامن التغييرات عند عودة الاتصال

🛠️ استكشاف الأخطاء:

❌ "فشل الاتصال":
• تحقق من صحة مفتاح API
• تأكد من تفعيل Firestore API
• تحقق من اتصال الإنترنت

❌ "خطأ في الصلاحيات":
• تحقق من صحة مفتاح API
• تأكد من صلاحيات المشروع
• جرب إنشاء مفتاح API جديد

❌ "لا توجد بيانات":
• تحقق من اسم المجموعة
• تأكد من وجود بيانات في Firestore
• جرب إعادة تحميل البيانات

🔄 التحديث من JSON المحلي:
إذا كان لديك بيانات محفوظة محلياً:
1. شغل البرنامج بدون Firestore أولاً
2. تأكد من ظهور جميع البيانات
3. فعل Firestore - سيتم رفع البيانات تلقائياً

📞 الدعم الفني:
• راجع ملفات التعليمات في مجلد البرنامج
• تحقق من رسائل الحالة في البرنامج
• استخدم وضع Debug لمزيد من التفاصيل

🎉 استمتع بالمزامنة السحابية مع Inzo IB!

تاريخ الإنشاء: 2025-01-21
إصدار البرنامج: Inzo IB v7.4 مع دعم Cloud Firestore
