# دليل تأمين Firestore - منع التعديل إلا من UID محدد
## Firestore Security Guide - Restrict Modifications to Specific UID

### 🎯 الهدف
تأمين قاعدة بيانات Firestore لمنع التعديل إلا من جهاز واحد محدد بـ UID فريد، مع الحفاظ على إمكانية القراءة من جميع الأجهزة.

### 🔐 آلية الأمان المطبقة

#### 1. توليد معرف الجهاز (Device UID)
```csharp
private string GenerateDeviceUID()
{
    var machineInfo = Environment.MachineName + Environment.UserName + Environment.OSVersion.ToString();
    using (var sha256 = SHA256.Create())
    {
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo + securityKey));
        return Convert.ToBase64String(hash).Substring(0, 16);
    }
}
```

#### 2. فحص الصلاحيات
```csharp
private bool IsAuthorizedToModify()
{
    if (string.IsNullOrEmpty(authorizedUID))
        return true; // السماح إذا لم يتم تعيين UID
    
    return deviceUID == authorizedUID;
}
```

#### 3. رمز المصادقة (Auth Token)
- يتم توليد رمز مصادقة فريد لكل جهاز
- الرمز صالح لـ 24 ساعة
- يتم تجديده تلقائياً عند انتهاء الصلاحية

### 🛡️ مستويات الحماية

#### المستوى الأول: فحص UID المحلي
- فحص معرف الجهاز قبل أي عملية كتابة
- منع العمليات محلياً إذا لم يكن الجهاز مصرحاً

#### المستوى الثاني: رمز المصادقة
- إضافة رمز مصادقة مشفر لكل عملية
- التحقق من صحة الرمز وتاريخ انتهاء صلاحيته

#### المستوى الثالث: معلومات الجهاز
- حفظ معلومات الجهاز مع كل عملية تعديل
- تتبع من قام بالتعديل ومتى

### 📋 الميزات المطبقة

#### 1. تعيين الجهاز المصرح
```csharp
public void SetAuthorizedUID(string uid)
{
    authorizedUID = uid;
    SaveSettings();
}
```

#### 2. حماية عمليات الكتابة
- **SaveDataAsync**: محمية بفحص الصلاحيات
- **SyncSettingAsync**: محمية بفحص الصلاحيات
- جميع عمليات التعديل تتطلب تصريح

#### 3. معلومات الأمان في البيانات
```csharp
// معلومات الأمان والمصادقة
DeviceUID = new { stringValue = deviceUID ?? "" },
AuthToken = new { stringValue = authToken ?? "" },
LastModified = new { timestampValue = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") },
ModifiedBy = new { stringValue = Environment.MachineName ?? "" }
```

### 🎮 واجهة المستخدم

#### زر إعدادات الأمان (🔐)
- عرض معرف الجهاز الحالي
- تعيين الجهاز كمصرح وحيد
- إزالة قيود الأمان

#### خيارات الأمان:
1. **تعيين هذا الجهاز كمصرح**: منع جميع الأجهزة الأخرى من التعديل
2. **إزالة القيود**: السماح لجميع الأجهزة بالتعديل
3. **إلغاء**: عدم تغيير الإعدادات

### 🔧 كيفية الاستخدام

#### 1. تفعيل الأمان
```
1. اضغط على زر الإعدادات (⚙️)
2. اختر "🔐 إعدادات الأمان"
3. اضغط "نعم" لتعيين هذا الجهاز كمصرح وحيد
4. سيتم منع جميع الأجهزة الأخرى من التعديل
```

#### 2. إزالة الأمان
```
1. اضغط على زر الإعدادات (⚙️)
2. اختر "🔐 إعدادات الأمان"
3. اضغط "لا" لإزالة قيود الأمان
4. ستتمكن جميع الأجهزة من التعديل مرة أخرى
```

#### 3. عرض معرف الجهاز
```
1. اضغط على زر الإعدادات (⚙️)
2. اختر "🔐 إعدادات الأمان"
3. سيظهر معرف الجهاز الحالي في النافذة
```

### 📊 رسائل النظام

#### رسائل النجاح:
- `✅ تم تعيين هذا الجهاز كالوحيد المصرح بالتعديل`
- `✅ تم إزالة قيود الأمان - جميع الأجهزة يمكنها التعديل الآن`
- `🔐 تم تعيين UID المصرح: [UID]`

#### رسائل الحماية:
- `🚫 غير مصرح بالتعديل - تم رفض العملية`
- `🚫 غير مصرح بمزامنة الإعدادات - تم رفض العملية`

#### رسائل المعلومات:
- `🔐 فحص الصلاحية: Device=[UID], Authorized=[UID], Result=[true/false]`
- `✅ تم مزامنة الإعداد [key] مع Firestore (مصادق)`

### 🔍 التشخيص والمراقبة

#### سجل الأحداث:
- جميع محاولات التعديل يتم تسجيلها
- عرض معرف الجهاز المحاول والنتيجة
- تتبع عمليات المصادقة والرموز

#### معلومات الجهاز:
- معرف الجهاز الفريد (16 حرف)
- اسم الجهاز والمستخدم
- نظام التشغيل

### ⚠️ تحذيرات مهمة

1. **احفظ معرف الجهاز**: احتفظ بنسخة من معرف الجهاز المصرح في مكان آمن
2. **النسخ الاحتياطي**: تأكد من وجود نسخة احتياطية قبل تفعيل الأمان
3. **الوصول الطارئ**: في حالة فقدان الوصول، ستحتاج لإعادة تعيين الإعدادات يدوياً
4. **الأجهزة المتعددة**: تذكر أن تفعيل الأمان سيمنع جميع الأجهزة الأخرى من التعديل

### 🔄 استعادة الوصول

في حالة فقدان الوصول:
1. احذف ملف إعدادات Firestore من الجهاز المصرح
2. أعد تكوين الإعدادات
3. عطل الأمان مؤقتاً
4. أعد تفعيل الأمان من الجهاز الجديد

### 📈 الفوائد

1. **حماية البيانات**: منع التعديل غير المصرح به
2. **تتبع التغييرات**: معرفة من قام بالتعديل ومتى
3. **مرونة الإدارة**: إمكانية تفعيل/إلغاء الأمان بسهولة
4. **شفافية كاملة**: عرض جميع معلومات الأمان للمستخدم
5. **أمان متعدد المستويات**: حماية محلية وسحابية

### 🎯 الخلاصة

تم تطبيق نظام أمان شامل لـ Firestore يمنع التعديل إلا من UID محدد، مع الحفاظ على سهولة الاستخدام ومرونة الإدارة. النظام يوفر حماية متعددة المستويات مع إمكانية المراقبة والتشخيص الكامل.
