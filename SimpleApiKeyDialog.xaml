<Window x:Class="InzoIB_Simple.SimpleApiKeyDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدخال مفتاح OpenAI API" Height="250" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        WindowStyle="SingleBorderWindow"
        ResizeMode="NoResize"
        Background="#f8f9fa">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#161642" Padding="20,15">
            <TextBlock Text="🤖 إدخال مفتاح OpenAI API" 
                       FontSize="16" FontWeight="Bold" 
                       Foreground="White" HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <StackPanel Grid.Row="1" Margin="30,20" VerticalAlignment="Center">
            <TextBlock Text="أدخل مفتاح OpenAI API:" 
                       FontSize="14" FontWeight="Bold" 
                       Foreground="#161642" 
                       Margin="0,0,0,10"/>
            
            <TextBox x:Name="ApiKeyTextBox"
                     Height="40" FontSize="14"
                     Padding="15,10"
                     BorderBrush="#ddd" BorderThickness="2"
                     Background="White"
                     VerticalContentAlignment="Center"
                     Margin="0,0,0,20"
                     KeyDown="ApiKeyTextBox_KeyDown"/>
            
            <TextBlock Text="💡 المفتاح سيتم حفظه بشكل آمن على جهازك" 
                       FontSize="11" 
                       Foreground="#666" 
                       HorizontalAlignment="Center"/>
        </StackPanel>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#f0f0f0" BorderBrush="#ddd" BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="OkButton" Content="✅ موافق" 
                        Background="#4caf50" Foreground="White" 
                        Padding="20,10" Margin="0,0,15,0" FontSize="12" FontWeight="Bold"
                        Click="OkButton_Click" MinWidth="100"/>
                
                <Button x:Name="CancelButton" Content="❌ إلغاء" 
                        Background="#757575" Foreground="White" 
                        Padding="20,10" FontSize="12" FontWeight="Bold"
                        Click="CancelButton_Click" MinWidth="100"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
