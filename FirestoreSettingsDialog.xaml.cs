using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Diagnostics;
using System.Windows.Threading;

namespace InzoIB_Simple
{
    /// <summary>
    /// نافذة إعدادات Cloud Firestore
    /// </summary>
    public partial class FirestoreSettingsDialog : Window
    {
        private FirestoreManager firestoreManager;
        private DispatcherTimer textVisibilityTimer;
        
        /// <summary>
        /// معرف المشروع المدخل
        /// </summary>
        public string ProjectId { get; private set; }
        
        /// <summary>
        /// مفتاح API المدخل
        /// </summary>
        public string ApiKey { get; private set; }
        
        /// <summary>
        /// اسم المجموعة المدخل
        /// </summary>
        public string CollectionName { get; private set; }
        
        /// <summary>
        /// هل تم حفظ الإعدادات بنجاح
        /// </summary>
        public bool IsSettingsSaved { get; private set; } = false;

        /// <summary>
        /// إنشاء نافذة إعدادات Firestore
        /// </summary>
        /// <param name="manager">مدير Firestore</param>
        public FirestoreSettingsDialog(FirestoreManager manager)
        {
            InitializeComponent();
            firestoreManager = manager;

            // ربط حدث تحميل النافذة
            this.Loaded += FirestoreSettingsDialog_Loaded;

            // تحميل الإعدادات الحالية إن وجدت
            LoadCurrentSettings();

            // إجبار إعدادات النص لضمان الظهور
            ForceTextVisibility();

            // إنشاء Timer لإجبار الإعدادات بشكل دوري
            StartTextVisibilityTimer();
        }

        /// <summary>
        /// إجبار ظهور النص في جميع الحقول
        /// </summary>
        private void ForceTextVisibility()
        {
            try
            {
                // إعداد ProjectIdTextBox
                ProjectIdTextBox.Background = new SolidColorBrush(Colors.White);
                ProjectIdTextBox.Foreground = new SolidColorBrush(Colors.Black);
                ProjectIdTextBox.BorderBrush = new SolidColorBrush(Colors.Blue);
                ProjectIdTextBox.CaretBrush = new SolidColorBrush(Colors.Black);
                ProjectIdTextBox.SelectionBrush = new SolidColorBrush(Colors.LightBlue);
                ProjectIdTextBox.FontSize = 14;
                ProjectIdTextBox.FontFamily = new FontFamily("Arial");
                ProjectIdTextBox.Height = 45;
                ProjectIdTextBox.Padding = new Thickness(10, 12, 10, 8);
                ProjectIdTextBox.VerticalContentAlignment = VerticalAlignment.Center;

                // إعداد ApiKeyPasswordBox
                ApiKeyPasswordBox.Background = new SolidColorBrush(Colors.White);
                ApiKeyPasswordBox.Foreground = new SolidColorBrush(Colors.Black);
                ApiKeyPasswordBox.BorderBrush = new SolidColorBrush(Colors.Blue);
                ApiKeyPasswordBox.CaretBrush = new SolidColorBrush(Colors.Black);
                ApiKeyPasswordBox.SelectionBrush = new SolidColorBrush(Colors.LightBlue);
                ApiKeyPasswordBox.FontSize = 14;
                ApiKeyPasswordBox.FontFamily = new FontFamily("Arial");
                ApiKeyPasswordBox.Height = 45;
                ApiKeyPasswordBox.Padding = new Thickness(10, 12, 10, 8);
                ApiKeyPasswordBox.VerticalContentAlignment = VerticalAlignment.Center;

                // إعداد CollectionNameTextBox
                CollectionNameTextBox.Background = new SolidColorBrush(Colors.White);
                CollectionNameTextBox.Foreground = new SolidColorBrush(Colors.Black);
                CollectionNameTextBox.BorderBrush = new SolidColorBrush(Colors.Blue);
                CollectionNameTextBox.CaretBrush = new SolidColorBrush(Colors.Black);
                CollectionNameTextBox.SelectionBrush = new SolidColorBrush(Colors.LightBlue);
                CollectionNameTextBox.FontSize = 14;
                CollectionNameTextBox.FontFamily = new FontFamily("Arial");
                CollectionNameTextBox.Height = 45;
                CollectionNameTextBox.Padding = new Thickness(10, 12, 10, 8);
                CollectionNameTextBox.VerticalContentAlignment = VerticalAlignment.Center;

                Debug.WriteLine("✅ تم إجبار إعدادات النص لضمان الظهور");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إجبار إعدادات النص: {ex.Message}");
            }
        }

        /// <summary>
        /// بدء Timer لإجبار إعدادات النص بشكل دوري
        /// </summary>
        private void StartTextVisibilityTimer()
        {
            try
            {
                textVisibilityTimer = new DispatcherTimer();
                textVisibilityTimer.Interval = TimeSpan.FromMilliseconds(500); // كل نصف ثانية
                textVisibilityTimer.Tick += (sender, e) => ForceTextVisibility();
                textVisibilityTimer.Start();

                Debug.WriteLine("✅ تم بدء Timer لإجبار إعدادات النص");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في بدء Timer: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج حدث تحميل النافذة
        /// </summary>
        private void FirestoreSettingsDialog_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // التأكد من ظهور النص بوضوح عند تحميل النافذة
                ProjectIdTextBox.Focus();
                ProjectIdTextBox.SelectAll();

                // إعادة تعيين الألوان للتأكد
                ProjectIdTextBox.Foreground = Brushes.White;
                ApiKeyPasswordBox.Foreground = Brushes.White;
                CollectionNameTextBox.Foreground = Brushes.White;

                Debug.WriteLine("✅ تم تحميل نافذة إعدادات Firestore");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تحميل نافذة الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الإعدادات الحالية
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // تعيين قيم افتراضية لضمان ظهور النص
                if (string.IsNullOrEmpty(ProjectIdTextBox.Text))
                {
                    ProjectIdTextBox.Text = "";
                    ProjectIdTextBox.Foreground = Brushes.White;
                }

                if (string.IsNullOrEmpty(ApiKeyPasswordBox.Password))
                {
                    ApiKeyPasswordBox.Foreground = Brushes.White;
                }

                if (string.IsNullOrEmpty(CollectionNameTextBox.Text))
                {
                    CollectionNameTextBox.Text = "inzo_content";
                    CollectionNameTextBox.Foreground = Brushes.White;
                }

                // التأكد من ظهور النص بوضوح
                ProjectIdTextBox.CaretBrush = Brushes.White;
                ApiKeyPasswordBox.CaretBrush = Brushes.White;
                CollectionNameTextBox.CaretBrush = Brushes.White;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تحميل الإعدادات الحالية: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج حدث اختبار الاتصال
        /// </summary>
        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            await TestConnectionAsync();
        }

        /// <summary>
        /// اختبار الاتصال مع Firestore
        /// </summary>
        private async Task TestConnectionAsync()
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (!ValidateInput())
                {
                    return;
                }

                // تعطيل الأزرار أثناء الاختبار
                SetButtonsEnabled(false);
                ShowStatus("🔄 جاري اختبار الاتصال مع Firestore...", Brushes.Orange);

                // حفظ الإعدادات مؤقتاً
                var tempProjectId = ProjectIdTextBox.Text.Trim();
                var tempApiKey = ApiKeyPasswordBox.Password.Trim();
                var tempCollectionName = CollectionNameTextBox.Text.Trim();

                Debug.WriteLine($"🔄 اختبار الاتصال مع المشروع: {tempProjectId}");

                // اختبار مباشر للرابط أولاً
                var directTestResult = await TestDirectConnection(tempProjectId, tempApiKey, tempCollectionName);

                if (directTestResult.success)
                {
                    ShowStatus($"✅ {directTestResult.message}", Brushes.Green);
                    Debug.WriteLine("✅ اختبار الاتصال المباشر نجح");
                    return;
                }
                else
                {
                    ShowStatus($"❌ اختبار مباشر فشل: {directTestResult.message}", Brushes.Orange);
                    Debug.WriteLine($"❌ اختبار الاتصال المباشر فشل: {directTestResult.message}");
                }

                // إنشاء مدير Firestore مؤقت للاختبار
                var tempManager = new FirestoreManager(string.Empty);

                // ربط حدث تغيير الحالة لإظهار التفاصيل
                tempManager.ConnectionStatusChanged += (isConnected, message) =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        if (isConnected)
                        {
                            ShowStatus($"✅ {message}", Brushes.Green);
                        }
                        else
                        {
                            ShowStatus($"❌ {message}", Brushes.Red);
                        }
                    });
                };

                tempManager.SaveSettings(tempProjectId, tempApiKey, tempCollectionName);

                // محاولة الاتصال
                var isConnected = await tempManager.InitializeAsync();

                if (isConnected)
                {
                    ShowStatus("✅ تم الاتصال بنجاح! يمكنك الآن حفظ الإعدادات", Brushes.Green);
                    Debug.WriteLine("✅ اختبار الاتصال نجح");
                }
                else
                {
                    ShowStatus("❌ فشل الاتصال. جاري إجراء تشخيص مفصل...", Brushes.Orange);
                    Debug.WriteLine("❌ اختبار الاتصال فشل - بدء التشخيص");

                    // إجراء تشخيص مفصل
                    var diagnosis = await tempManager.DiagnoseConnectionAsync();
                    Debug.WriteLine("📋 تقرير التشخيص:");
                    Debug.WriteLine(diagnosis);

                    ShowStatus("❌ فشل الاتصال. راجع نافذة Debug للتشخيص المفصل", Brushes.Red);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ في الاتصال: {ex.Message}";
                ShowStatus($"❌ {errorMessage}", Brushes.Red);
                Debug.WriteLine($"❌ خطأ في اختبار الاتصال: {ex.Message}");
                Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }
            finally
            {
                // إعادة تفعيل الأزرار
                SetButtonsEnabled(true);
            }
        }

        /// <summary>
        /// اختبار مباشر للاتصال مع Firestore
        /// </summary>
        private async Task<(bool success, string message)> TestDirectConnection(string projectId, string apiKey, string collectionName)
        {
            try
            {
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(30);

                    // الرابط الذي قدمته
                    var testUrl = $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)/documents/{collectionName}?key={apiKey}";

                    Debug.WriteLine($"🔗 اختبار مباشر للرابط: {testUrl}");

                    var response = await httpClient.GetAsync(testUrl);
                    var content = await response.Content.ReadAsStringAsync();

                    Debug.WriteLine($"📡 رمز الاستجابة المباشر: {response.StatusCode}");
                    Debug.WriteLine($"📄 محتوى الاستجابة المباشر: {content.Substring(0, Math.Min(300, content.Length))}");

                    if (response.IsSuccessStatusCode)
                    {
                        return (true, "تم الاتصال المباشر بنجاح!");
                    }
                    else
                    {
                        var errorMsg = $"فشل الاتصال المباشر: {response.StatusCode}";
                        if (content.Contains("API key not valid"))
                        {
                            errorMsg = "مفتاح API غير صالح";
                        }
                        else if (content.Contains("not found"))
                        {
                            errorMsg = "المشروع أو المجموعة غير موجودة";
                        }
                        else if (response.StatusCode == System.Net.HttpStatusCode.Forbidden)
                        {
                            errorMsg = "لا توجد صلاحيات - تحقق من تفعيل Firestore API";
                        }

                        return (false, errorMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في الاختبار المباشر: {ex.Message}");
                return (false, $"خطأ في الاختبار المباشر: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج حدث حفظ الإعدادات
        /// </summary>
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            await SaveSettingsAsync();
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private async Task SaveSettingsAsync()
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (!ValidateInput())
                {
                    return;
                }

                // تعطيل الأزرار أثناء الحفظ
                SetButtonsEnabled(false);
                ShowStatus("💾 جاري حفظ الإعدادات...", Brushes.Orange);

                // حفظ البيانات
                ProjectId = ProjectIdTextBox.Text.Trim();
                ApiKey = ApiKeyPasswordBox.Password.Trim();
                CollectionName = CollectionNameTextBox.Text.Trim();

                // حفظ الإعدادات في مدير Firestore
                firestoreManager.SaveSettings(ProjectId, ApiKey, CollectionName);

                // محاولة تهيئة الاتصال
                var isInitialized = await firestoreManager.InitializeAsync();

                if (isInitialized)
                {
                    IsSettingsSaved = true;
                    ShowStatus("✅ تم حفظ الإعدادات وتهيئة الاتصال بنجاح!", Brushes.Green);
                    
                    // إغلاق النافذة بعد ثانيتين
                    await Task.Delay(2000);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    ShowStatus("❌ تم حفظ الإعدادات لكن فشل في تهيئة الاتصال", Brushes.Orange);
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"❌ خطأ في حفظ الإعدادات: {ex.Message}", Brushes.Red);
                Debug.WriteLine($"❌ خطأ في حفظ الإعدادات: {ex.Message}");
            }
            finally
            {
                // إعادة تفعيل الأزرار
                SetButtonsEnabled(true);
            }
        }

        /// <summary>
        /// معالج حدث إلغاء
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// معالج حدث التركيز على مربع النص
        /// </summary>
        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // إجبار إعدادات النص
                textBox.Background = new SolidColorBrush(Colors.White);
                textBox.Foreground = new SolidColorBrush(Colors.Black);
                textBox.CaretBrush = new SolidColorBrush(Colors.Black);
                textBox.SelectionBrush = new SolidColorBrush(Colors.LightBlue);
                textBox.FontSize = 14;
                textBox.FontFamily = new FontFamily("Arial");
                textBox.Height = 45;
                textBox.Padding = new Thickness(10, 12, 10, 8);
                textBox.VerticalContentAlignment = VerticalAlignment.Center;

                // تحديد كامل النص عند التركيز
                textBox.SelectAll();

                Debug.WriteLine($"✅ تم إجبار إعدادات النص للحقل: {textBox.Name}");
            }
        }

        /// <summary>
        /// معالج حدث التركيز على مربع كلمة المرور
        /// </summary>
        private void PasswordBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                // إجبار إعدادات النص
                passwordBox.Background = new SolidColorBrush(Colors.White);
                passwordBox.Foreground = new SolidColorBrush(Colors.Black);
                passwordBox.CaretBrush = new SolidColorBrush(Colors.Black);
                passwordBox.SelectionBrush = new SolidColorBrush(Colors.LightBlue);
                passwordBox.FontSize = 14;
                passwordBox.FontFamily = new FontFamily("Arial");
                passwordBox.Height = 45;
                passwordBox.Padding = new Thickness(10, 12, 10, 8);
                passwordBox.VerticalContentAlignment = VerticalAlignment.Center;

                // تحديد كامل النص عند التركيز
                passwordBox.SelectAll();

                Debug.WriteLine($"✅ تم إجبار إعدادات النص لحقل كلمة المرور: {passwordBox.Name}");
            }
        }

        /// <summary>
        /// معالج حدث تغيير النص
        /// </summary>
        private void TextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // إجبار إعدادات النص عند كل تغيير
                textBox.Foreground = new SolidColorBrush(Colors.Black);
                textBox.CaretBrush = new SolidColorBrush(Colors.Black);

                Debug.WriteLine($"✅ تم إجبار لون النص عند التغيير للحقل: {textBox.Name}");
            }
        }

        /// <summary>
        /// معالج حدث تغيير كلمة المرور
        /// </summary>
        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                // إجبار إعدادات النص عند كل تغيير
                passwordBox.Foreground = new SolidColorBrush(Colors.Black);
                passwordBox.CaretBrush = new SolidColorBrush(Colors.Black);

                Debug.WriteLine($"✅ تم إجبار لون النص عند التغيير لحقل كلمة المرور: {passwordBox.Name}");
            }
        }



        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            // التحقق من معرف المشروع
            if (string.IsNullOrWhiteSpace(ProjectIdTextBox.Text))
            {
                ShowStatus("❌ يرجى إدخال معرف المشروع", Brushes.Red);
                ProjectIdTextBox.Focus();
                return false;
            }

            // التحقق من مفتاح API
            if (string.IsNullOrWhiteSpace(ApiKeyPasswordBox.Password))
            {
                ShowStatus("❌ يرجى إدخال مفتاح API", Brushes.Red);
                ApiKeyPasswordBox.Focus();
                return false;
            }

            // التحقق من اسم المجموعة
            if (string.IsNullOrWhiteSpace(CollectionNameTextBox.Text))
            {
                ShowStatus("❌ يرجى إدخال اسم المجموعة", Brushes.Red);
                CollectionNameTextBox.Focus();
                return false;
            }

            // التحقق من صحة اسم المجموعة (لا يحتوي على أحرف غير مسموحة)
            var collectionName = CollectionNameTextBox.Text.Trim();
            if (collectionName.Contains("/") || collectionName.Contains("\\"))
            {
                ShowStatus("❌ اسم المجموعة لا يجب أن يحتوي على / أو \\", Brushes.Red);
                CollectionNameTextBox.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// عرض رسالة الحالة
        /// </summary>
        private void ShowStatus(string message, Brush color)
        {
            StatusPanel.Visibility = Visibility.Visible;
            StatusText.Text = message;
            StatusText.Foreground = color;
        }

        /// <summary>
        /// تفعيل أو تعطيل الأزرار
        /// </summary>
        private void SetButtonsEnabled(bool enabled)
        {
            TestConnectionButton.IsEnabled = enabled;
            SaveButton.IsEnabled = enabled;
            CancelButton.IsEnabled = enabled;
            
            // تغيير محتوى الأزرار أثناء التحميل
            if (!enabled)
            {
                TestConnectionButton.Content = "🔄 جاري الاختبار...";
                SaveButton.Content = "💾 جاري الحفظ...";
            }
            else
            {
                TestConnectionButton.Content = "🔍 اختبار الاتصال";
                SaveButton.Content = "💾 حفظ الإعدادات";
            }
        }
    }
}
