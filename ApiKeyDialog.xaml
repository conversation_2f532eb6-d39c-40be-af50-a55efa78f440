<Window x:Class="InzoIB_Simple.ApiKeyDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات OpenAI API" Height="400" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        WindowStyle="SingleBorderWindow"
        ResizeMode="NoResize"
        Icon="inzo.ico">
    
    <Grid Background="#f8f9fa">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#161642" Padding="20,15">
            <StackPanel>
                <TextBlock Text="🤖 إعدادات الذكاء الاصطناعي OpenAI" 
                           FontSize="18" FontWeight="Bold" 
                           Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="أدخل مفتاح API الخاص بك لتفعيل البحث الذكي" 
                           FontSize="12" 
                           Foreground="#E0E0E0" HorizontalAlignment="Center" 
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- API Key Input -->
                <TextBlock Text="🔑 مفتاح OpenAI API:" FontSize="14" FontWeight="Bold" 
                           Foreground="#161642" Margin="0,0,0,10"/>
                
                <TextBox x:Name="ApiKeyTextBox" 
                         Height="35" FontSize="12" 
                         Padding="10,8" 
                         BorderBrush="#ddd" BorderThickness="2"
                         Background="White"
                         Margin="0,0,0,15"/>

                <!-- Instructions -->
                <Border Background="#e3f2fd" BorderBrush="#2196f3" BorderThickness="1" 
                        CornerRadius="5" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📋 تعليمات الحصول على مفتاح API:" 
                                   FontSize="14" FontWeight="Bold" 
                                   Foreground="#1976d2" Margin="0,0,0,10"/>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="#424242" LineHeight="20">
                            <Run Text="1. اذهب إلى موقع OpenAI: "/>
                            <Hyperlink NavigateUri="https://platform.openai.com/api-keys" RequestNavigate="Hyperlink_RequestNavigate">
                                <Run Text="https://platform.openai.com/api-keys"/>
                            </Hyperlink>
                            <LineBreak/>
                            <Run Text="2. قم بتسجيل الدخول أو إنشاء حساب جديد"/>
                            <LineBreak/>
                            <Run Text="3. اضغط على 'Create new secret key'"/>
                            <LineBreak/>
                            <Run Text="4. انسخ المفتاح والصقه في الخانة أعلاه"/>
                            <LineBreak/>
                            <Run Text="5. احفظ المفتاح في مكان آمن"/>
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- Security Notice -->
                <Border Background="#fff3e0" BorderBrush="#ff9800" BorderThickness="1" 
                        CornerRadius="5" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="⚠️ تنبيه أمني:" 
                                   FontSize="14" FontWeight="Bold" 
                                   Foreground="#f57c00" Margin="0,0,0,5"/>
                        
                        <TextBlock Text="• لا تشارك مفتاح API مع أي شخص آخر" 
                                   FontSize="12" Foreground="#424242" Margin="0,0,0,3"/>
                        <TextBlock Text="• سيتم حفظ المفتاح محلياً على جهازك فقط" 
                                   FontSize="12" Foreground="#424242" Margin="0,0,0,3"/>
                        <TextBlock Text="• يمكنك تغيير أو حذف المفتاح في أي وقت" 
                                   FontSize="12" Foreground="#424242"/>
                    </StackPanel>
                </Border>

                <!-- Current Status -->
                <Border x:Name="StatusBorder" Background="#f5f5f5" BorderBrush="#ddd" BorderThickness="1" 
                        CornerRadius="5" Padding="15">
                    <StackPanel>
                        <TextBlock Text="📊 الحالة الحالية:" 
                                   FontSize="14" FontWeight="Bold" 
                                   Foreground="#161642" Margin="0,0,0,5"/>
                        
                        <TextBlock x:Name="StatusText" 
                                   FontSize="12" Foreground="#666"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#f0f0f0" BorderBrush="#ddd" BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="TestButton" Content="🧪 اختبار المفتاح" 
                        Background="#4caf50" Foreground="White" 
                        Padding="15,8" Margin="0,0,10,0" FontSize="12" FontWeight="Bold"
                        Click="TestButton_Click" MinWidth="120"/>
                
                <Button x:Name="SaveButton" Content="💾 حفظ" 
                        Background="#2196f3" Foreground="White" 
                        Padding="15,8" Margin="0,0,10,0" FontSize="12" FontWeight="Bold"
                        Click="SaveButton_Click" MinWidth="80"/>
                
                <Button x:Name="ClearButton" Content="🗑️ مسح" 
                        Background="#ff5722" Foreground="White" 
                        Padding="15,8" Margin="0,0,10,0" FontSize="12" FontWeight="Bold"
                        Click="ClearButton_Click" MinWidth="80"/>
                
                <Button x:Name="CancelButton" Content="❌ إلغاء" 
                        Background="#757575" Foreground="White" 
                        Padding="15,8" FontSize="12" FontWeight="Bold"
                        Click="CancelButton_Click" MinWidth="80"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
