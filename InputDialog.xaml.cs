using System;
using System.Windows;

namespace InzoIB_Simple
{
    public partial class InputDialog : Window
    {
        public string InputTitle { get; private set; }
        public string InputContent { get; private set; }

        public InputDialog(string sectionName)
        {
            InitializeComponent();

            // تخصيص النافذة حسب القسم
            HeaderText.Text = $"إضافة محتوى جديد إلى قسم: {sectionName}";
            TitleTextBox.Text = "";
            ContentTextBox.Text = "";

            // التركيز على صندوق العنوان
            TitleTextBox.Focus();
        }

        public InputDialog(string sectionName, string existingTitle, string existingContent) : this(sectionName)
        {
            // للتعديل
            HeaderText.Text = $"تعديل المحتوى في قسم: {sectionName}";
            TitleTextBox.Text = existingTitle;
            ContentTextBox.Text = existingContent;
            OkButton.Content = "✅ موافق - حفظ التعديل";
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من أن هناك عنوان أو محتوى على الأقل
            if (string.IsNullOrWhiteSpace(TitleTextBox.Text) && string.IsNullOrWhiteSpace(ContentTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان أو محتوى على الأقل", "خطأ في البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TitleTextBox.Focus();
                return;
            }

            // حفظ البيانات (العنوان والمحتوى يمكن أن يكونا فارغين منفردين)
            InputTitle = string.IsNullOrWhiteSpace(TitleTextBox.Text) ? "" : TitleTextBox.Text.Trim();
            InputContent = string.IsNullOrWhiteSpace(ContentTextBox.Text) ? "" : ContentTextBox.Text.Trim();

            // إغلاق النافذة بنجاح
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية
            DialogResult = false;
            Close();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // التأكد من ظهور النافذة في المقدمة
            this.Activate();
            this.Focus();
        }
    }
}
