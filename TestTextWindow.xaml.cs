using System;
using System.Windows;
using System.Windows.Media;

namespace InzoIB_Simple
{
    public partial class TestTextWindow : Window
    {
        public TestTextWindow()
        {
            InitializeComponent();
            
            // إجبار إعدادات النص فور التحميل
            ForceTextSettings();
        }
        
        private void ForceTextSettings()
        {
            try
            {
                // إجبار إعدادات SimpleTextBox
                SimpleTextBox.Background = new SolidColorBrush(Colors.White);
                SimpleTextBox.Foreground = new SolidColorBrush(Colors.Black);
                SimpleTextBox.CaretBrush = new SolidColorBrush(Colors.Black);
                SimpleTextBox.FontSize = 14;
                
                // إجبار إعدادات SimplePasswordBox
                SimplePasswordBox.Background = new SolidColorBrush(Colors.White);
                SimplePasswordBox.Foreground = new SolidColorBrush(Colors.Black);
                SimplePasswordBox.CaretBrush = new SolidColorBrush(Colors.Black);
                SimplePasswordBox.FontSize = 14;
                
                // إجبار إعدادات StyledTextBox
                StyledTextBox.Background = new SolidColorBrush(Colors.Yellow);
                StyledTextBox.Foreground = new SolidColorBrush(Colors.Red);
                StyledTextBox.CaretBrush = new SolidColorBrush(Colors.Red);
                StyledTextBox.FontSize = 14;
                StyledTextBox.FontWeight = FontWeights.Bold;
                
                ResultTextBlock.Text = "✅ تم إجبار إعدادات النص";
            }
            catch (Exception ex)
            {
                ResultTextBlock.Text = $"❌ خطأ: {ex.Message}";
            }
        }
        
        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // اختبار قراءة النصوص
                string simpleText = SimpleTextBox.Text;
                string passwordText = SimplePasswordBox.Password;
                string styledText = StyledTextBox.Text;
                
                // إجبار الإعدادات مرة أخرى
                ForceTextSettings();
                
                // عرض النتيجة
                ResultTextBlock.Text = $"✅ النصوص: '{simpleText}' | كلمة المرور: {passwordText.Length} حرف | النمط: '{styledText}'";
                
                // إضافة نص تجريبي
                SimpleTextBox.Text = "تم الاختبار - " + DateTime.Now.ToString("HH:mm:ss");
                StyledTextBox.Text = "نص محدث - " + DateTime.Now.ToString("HH:mm:ss");
            }
            catch (Exception ex)
            {
                ResultTextBlock.Text = $"❌ خطأ في الاختبار: {ex.Message}";
            }
        }
    }
}
