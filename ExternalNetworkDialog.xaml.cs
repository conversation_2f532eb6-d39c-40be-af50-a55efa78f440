using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Text.Json;
using System.IO;

namespace InzoIB_Simple
{
    /// <summary>
    /// فئة لتمثيل عنوان IP خارجي
    /// </summary>
    public class ExternalIPInfo
    {
        public string IP { get; set; }
        public string Description { get; set; }
        public string Status { get; set; } = "غير مختبر";
        public DateTime LastTested { get; set; }
        public bool IsOnline { get; set; } = false;
    }

    /// <summary>
    /// نافذة إعدادات الشبكة الخارجية
    /// </summary>
    public partial class ExternalNetworkDialog : Window
    {
        private ObservableCollection<ExternalIPInfo> externalIPs;
        private readonly string configFilePath;

        public List<ExternalIPInfo> ExternalIPs => externalIPs.ToList();

        public ExternalNetworkDialog(List<ExternalIPInfo> currentIPs = null)
        {
            InitializeComponent();
            
            // مسار ملف الإعدادات
            configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "external_networks.json");
            
            // تهيئة القائمة
            externalIPs = new ObservableCollection<ExternalIPInfo>();
            
            // تحميل العناوين الحالية
            if (currentIPs != null)
            {
                foreach (var ip in currentIPs)
                {
                    externalIPs.Add(ip);
                }
            }
            else
            {
                LoadFromFile();
            }
            
            // ربط البيانات
            IPListBox.ItemsSource = externalIPs;
            
            // تركيز على حقل الإدخال
            IPTextBox.Focus();
        }

        /// <summary>
        /// تحميل العناوين من الملف
        /// </summary>
        private void LoadFromFile()
        {
            try
            {
                if (File.Exists(configFilePath))
                {
                    var json = File.ReadAllText(configFilePath);
                    var loadedIPs = JsonSerializer.Deserialize<List<ExternalIPInfo>>(json);
                    
                    if (loadedIPs != null)
                    {
                        foreach (var ip in loadedIPs)
                        {
                            externalIPs.Add(ip);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل إعدادات الشبكة:\n{ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// حفظ العناوين في الملف
        /// </summary>
        private void SaveToFile()
        {
            try
            {
                var json = JsonSerializer.Serialize(externalIPs.ToList(), new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                File.WriteAllText(configFilePath, json);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ إعدادات الشبكة:\n{ex.Message}", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// إضافة عنوان IP جديد
        /// </summary>
        private async void AddIP_Click(object sender, RoutedEventArgs e)
        {
            var ip = IPTextBox.Text.Trim();
            var description = DescriptionTextBox.Text.Trim();

            if (string.IsNullOrEmpty(ip))
            {
                MessageBox.Show("يرجى إدخال عنوان IP", "تنبيه", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                IPTextBox.Focus();
                return;
            }

            // التحقق من صحة عنوان IP
            if (!IsValidIPOrHostname(ip))
            {
                MessageBox.Show("عنوان IP غير صحيح. استخدم تنسيق مثل: ************* أو example.com", 
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                IPTextBox.Focus();
                return;
            }

            // التحقق من عدم التكرار
            if (externalIPs.Any(x => x.IP.Equals(ip, StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("هذا العنوان موجود بالفعل", "تنبيه", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // إضافة العنوان
            var newIP = new ExternalIPInfo
            {
                IP = ip,
                Description = string.IsNullOrEmpty(description) ? "بدون وصف" : description,
                Status = "جاري الاختبار...",
                LastTested = DateTime.Now
            };

            externalIPs.Add(newIP);

            // اختبار الاتصال
            AddIPButton.IsEnabled = false;
            await TestIPConnection(newIP);
            AddIPButton.IsEnabled = true;

            // مسح الحقول
            IPTextBox.Clear();
            DescriptionTextBox.Clear();
            IPTextBox.Focus();
        }

        /// <summary>
        /// اختبار عنوان IP محدد
        /// </summary>
        private async void TestIP_Click(object sender, RoutedEventArgs e)
        {
            var ip = IPTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(ip))
            {
                MessageBox.Show("يرجى إدخال عنوان IP للاختبار", "تنبيه", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!IsValidIPOrHostname(ip))
            {
                MessageBox.Show("عنوان IP غير صحيح", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            TestIPButton.IsEnabled = false;
            TestIPButton.Content = "🔄 جاري الاختبار...";

            try
            {
                var isOnline = await TestConnection(ip);
                var message = isOnline ? 
                    $"✅ الاتصال ناجح مع {ip}" : 
                    $"❌ فشل الاتصال مع {ip}";
                
                MessageBox.Show(message, "نتيجة الاختبار", 
                               MessageBoxButton.OK, 
                               isOnline ? MessageBoxImage.Information : MessageBoxImage.Warning);
            }
            finally
            {
                TestIPButton.IsEnabled = true;
                TestIPButton.Content = "🔍 اختبار";
            }
        }

        /// <summary>
        /// اختبار جميع العناوين
        /// </summary>
        private async void TestAllIPs_Click(object sender, RoutedEventArgs e)
        {
            if (externalIPs.Count == 0)
            {
                MessageBox.Show("لا توجد عناوين للاختبار", "تنبيه", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            TestAllButton.IsEnabled = false;
            TestAllButton.Content = "🔄 جاري الاختبار...";

            try
            {
                var tasks = externalIPs.Select(TestIPConnection).ToArray();
                await Task.WhenAll(tasks);
                
                var onlineCount = externalIPs.Count(ip => ip.IsOnline);
                MessageBox.Show($"تم اختبار {externalIPs.Count} عنوان\n" +
                               $"متصل: {onlineCount}\n" +
                               $"غير متصل: {externalIPs.Count - onlineCount}", 
                               "نتائج الاختبار", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            finally
            {
                TestAllButton.IsEnabled = true;
                TestAllButton.Content = "🔍 اختبار الكل";
            }
        }

        /// <summary>
        /// حذف عنوان IP
        /// </summary>
        private void RemoveIP_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ExternalIPInfo ipInfo)
            {
                var result = MessageBox.Show($"هل تريد حذف العنوان:\n{ipInfo.IP}\n({ipInfo.Description})?", 
                                           "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    externalIPs.Remove(ipInfo);
                }
            }
        }

        /// <summary>
        /// حذف جميع العناوين
        /// </summary>
        private void ClearAllIPs_Click(object sender, RoutedEventArgs e)
        {
            if (externalIPs.Count == 0)
            {
                MessageBox.Show("لا توجد عناوين للحذف", "تنبيه", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show($"هل تريد حذف جميع العناوين ({externalIPs.Count} عنوان)?", 
                                       "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                externalIPs.Clear();
            }
        }

        /// <summary>
        /// حفظ وإغلاق
        /// </summary>
        private void Save_Click(object sender, RoutedEventArgs e)
        {
            SaveToFile();
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// إلغاء
        /// </summary>
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// اختبار الاتصال مع عنوان IP
        /// </summary>
        private async Task TestIPConnection(ExternalIPInfo ipInfo)
        {
            ipInfo.Status = "جاري الاختبار...";
            ipInfo.LastTested = DateTime.Now;
            
            try
            {
                ipInfo.IsOnline = await TestConnection(ipInfo.IP);
                ipInfo.Status = ipInfo.IsOnline ? "✅ متصل" : "❌ غير متصل";
            }
            catch (Exception ex)
            {
                ipInfo.IsOnline = false;
                ipInfo.Status = $"❌ خطأ: {ex.Message}";
            }
        }

        /// <summary>
        /// اختبار الاتصال مع عنوان
        /// </summary>
        private async Task<bool> TestConnection(string ipOrHostname)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(10);
                    
                    // جرب المنافذ الشائعة للبرنامج
                    var ports = new[] { 8080, 8081, 8082, 8083 };
                    
                    foreach (var port in ports)
                    {
                        try
                        {
                            var response = await client.GetAsync($"http://{ipOrHostname}:{port}/ping");
                            if (response.IsSuccessStatusCode)
                            {
                                var content = await response.Content.ReadAsStringAsync();
                                if (content.Contains("InzoIB"))
                                {
                                    return true;
                                }
                            }
                        }
                        catch
                        {
                            // جرب المنفذ التالي
                            continue;
                        }
                    }
                }
            }
            catch
            {
                // فشل الاتصال
            }
            
            return false;
        }

        /// <summary>
        /// التحقق من صحة عنوان IP أو اسم المضيف
        /// </summary>
        private bool IsValidIPOrHostname(string input)
        {
            // التحقق من عنوان IP
            if (IPAddress.TryParse(input, out _))
            {
                return true;
            }
            
            // التحقق من اسم المضيف
            if (!string.IsNullOrEmpty(input) && 
                input.Length <= 253 && 
                !input.Contains(" ") &&
                !input.StartsWith(".") && 
                !input.EndsWith("."))
            {
                return true;
            }
            
            return false;
        }
    }
}
