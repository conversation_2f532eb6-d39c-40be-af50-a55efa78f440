using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Linq;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Runtime.InteropServices;
using System.Windows.Interop;
using System.Windows.Input;
using System.Net.Http;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using OpenAI;
using OpenAI.Chat;
using Telegram.Bot;
using Telegram.Bot.Exceptions;
using Telegram.Bot.Polling;
using TelegramTypes = Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;
using Telegram.Bot.Types.ReplyMarkups;
using System.Threading;
using IOFile = System.IO.File;
using MediaColor = System.Windows.Media.Color;
using System.Net;
using System.Net.Sockets;
using System.Text;
using Google.Cloud.Firestore;

namespace InzoIB_Simple
{
    // مدير المزامنة الشامل - يجمع جميع طرق المزامنة
    public class UniversalSyncManager
    {
        private readonly MainWindow mainWindow;
        private readonly NetworkSyncManager networkSync;
        private readonly FirestoreManager firestoreManager;
        private readonly TelegramBotClient telegramBot;
        private bool isEnabled = true;

        public UniversalSyncManager(MainWindow window, NetworkSyncManager networkSync,
                                  FirestoreManager firestoreManager, TelegramBotClient telegramBot = null)
        {
            this.mainWindow = window;
            this.networkSync = networkSync;
            this.firestoreManager = firestoreManager;
            this.telegramBot = telegramBot;
        }

        // مزامنة شاملة للبيانات عبر جميع الطرق
        public async Task SyncDataUniversallyAsync(List<ContentItem> contentItems, string changeType = "UPDATE")
        {
            if (!isEnabled) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"🌍 بدء المزامنة الشاملة: {contentItems.Count} عنصر - نوع التغيير: {changeType}");

                var tasks = new List<Task>();

                // 1. مزامنة عبر الشبكة المحلية
                if (networkSync != null)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            await networkSync.BroadcastDataSyncAsync(contentItems);
                            System.Diagnostics.Debug.WriteLine("✅ تمت المزامنة عبر الشبكة المحلية");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في المزامنة عبر الشبكة: {ex.Message}");
                        }
                    }));
                }

                // 2. مزامنة عبر Firestore
                if (firestoreManager != null)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            await firestoreManager.SaveDataAsync(contentItems);
                            System.Diagnostics.Debug.WriteLine("✅ تمت المزامنة عبر Firestore");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في المزامنة عبر Firestore: {ex.Message}");
                        }
                    }));
                }

                // 3. إشعار عبر التيليجرام (اختياري)
                if (telegramBot != null && changeType == "IMPORTANT_UPDATE")
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            // إرسال إشعار للمدير عن التحديث المهم
                            // يمكن تخصيص هذا حسب الحاجة
                            System.Diagnostics.Debug.WriteLine("📱 تم إرسال إشعار عبر التيليجرام");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في الإشعار عبر التيليجرام: {ex.Message}");
                        }
                    }));
                }

                // تنفيذ جميع المهام بالتوازي
                await Task.WhenAll(tasks);

                System.Diagnostics.Debug.WriteLine("🎉 انتهت المزامنة الشاملة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في المزامنة الشاملة: {ex.Message}");
            }
        }

        // مزامنة الإعدادات عبر جميع الطرق
        public async Task SyncSettingsUniversallyAsync(string settingKey, object settingValue)
        {
            if (!isEnabled) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"⚙️ بدء مزامنة الإعداد: {settingKey}");

                var tasks = new List<Task>();

                // مزامنة عبر الشبكة المحلية
                if (networkSync != null)
                {
                    tasks.Add(networkSync.BroadcastSettingsUpdateAsync(settingKey, settingValue));
                }

                // يمكن إضافة مزامنة الإعدادات عبر Firestore هنا إذا لزم الأمر

                await Task.WhenAll(tasks);

                System.Diagnostics.Debug.WriteLine($"✅ تمت مزامنة الإعداد {settingKey} بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة الإعداد: {ex.Message}");
            }
        }

        // تفعيل/إلغاء تفعيل المزامنة
        public void SetSyncEnabled(bool enabled)
        {
            isEnabled = enabled;
            System.Diagnostics.Debug.WriteLine($"🔄 تم {(enabled ? "تفعيل" : "إلغاء تفعيل")} المزامنة الشاملة");
        }

        // الحصول على حالة المزامنة
        public async Task<string> GetSyncStatusAsync()
        {
            var status = new StringBuilder();
            status.AppendLine("🌍 حالة المزامنة الشاملة");
            status.AppendLine("═══════════════════════");
            status.AppendLine($"📊 الحالة العامة: {(isEnabled ? "✅ مفعلة" : "❌ معطلة")}");
            status.AppendLine();

            // حالة مزامنة الشبكة المحلية
            if (networkSyncManager != null)
            {
                status.AppendLine("🌐 مزامنة الشبكة المحلية:");
                status.AppendLine($"   • الحالة: ✅ متاحة");
                status.AppendLine($"   • المنفذ: {networkSyncManager.Port}");
                status.AppendLine();
            }
            else
            {
                status.AppendLine("🌐 مزامنة الشبكة المحلية: ❌ غير متاحة");
                status.AppendLine();
            }

            // حالة مزامنة Firestore
            if (firestoreManager != null)
            {
                status.AppendLine("☁️ مزامنة Firestore:");
                status.AppendLine($"   • الحالة: ✅ متاحة");
                status.AppendLine();
            }
            else
            {
                status.AppendLine("☁️ مزامنة Firestore: ❌ غير متاحة");
                status.AppendLine();
            }

            // حالة بوت التيليجرام
            if (telegramBotManager != null)
            {
                status.AppendLine("🤖 بوت التيليجرام:");
                status.AppendLine($"   • الحالة: ✅ متاح");
                status.AppendLine();
            }
            else
            {
                status.AppendLine("🤖 بوت التيليجرام: ❌ غير متاح");
                status.AppendLine();
            }

            status.AppendLine($"⏰ آخر تحديث: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

            return status.ToString();
        }



        // مزامنة الإعدادات عبر جميع الطرق
        public async Task SyncSettingsUniversallyAsync(string settingKey, object settingValue)
        {
            if (!isEnabled)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ المزامنة الشاملة معطلة - تم تجاهل مزامنة الإعداد");
                return;
            }

            var tasks = new List<Task>();

            try
            {
                // مزامنة عبر الشبكة المحلية
                if (networkSyncManager != null)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            await networkSyncManager.SyncSettingAsync(settingKey, settingValue);
                            System.Diagnostics.Debug.WriteLine($"✅ تم مزامنة الإعداد عبر الشبكة المحلية: {settingKey}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة الإعداد عبر الشبكة المحلية: {ex.Message}");
                        }
                    }));
                }

                // مزامنة عبر Firestore
                if (firestoreManager != null)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            await firestoreManager.SyncSettingAsync(settingKey, settingValue);
                            System.Diagnostics.Debug.WriteLine($"✅ تم مزامنة الإعداد عبر Firestore: {settingKey}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة الإعداد عبر Firestore: {ex.Message}");
                        }
                    }));
                }

                // انتظار اكتمال جميع المهام
                await Task.WhenAll(tasks);
                System.Diagnostics.Debug.WriteLine($"🌍 تم إكمال مزامنة الإعداد الشاملة: {settingKey}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة الإعداد الشاملة: {ex.Message}");
            }
        }
    }

    // فئة لإدارة مزامنة الرسائل والتحديثات عبر الشبكة
    public class NetworkSyncManager
    {
        private HttpListener httpListener;
        private readonly int port;
        private readonly List<string> connectedDevices;
        private readonly MainWindow mainWindow;
        private readonly List<ExternalIPInfo> externalNetworks;
        private bool isRunning = false;

        public event Action<string, string, DateTime> MessageReceived;
        public event Action<string> DeviceConnected;
        public event Action<string> DeviceDisconnected;

        // أحداث جديدة للمزامنة الشاملة
        public event Action<List<ContentItem>> DataSyncReceived;
        public event Action<string, object> SettingsUpdateReceived;
        public event Action<string> ConfigurationUpdateReceived;

        public NetworkSyncManager(MainWindow window, List<ExternalIPInfo> externalNetworks = null, int port = 8080)
        {
            this.mainWindow = window;
            this.port = port;
            this.connectedDevices = new List<string>();
            this.externalNetworks = externalNetworks ?? new List<ExternalIPInfo>();
        }

        public async Task StartServerAsync()
        {
            try
            {
                // محاولة بدء الخادم مع معالجة أفضل للأخطاء
                httpListener = new HttpListener();

                // إضافة عدة prefixes للتأكد من العمل
                httpListener.Prefixes.Add($"http://+:{port}/");
                httpListener.Prefixes.Add($"http://localhost:{port}/");

                var localIP = GetLocalIPAddress();
                if (localIP != null)
                {
                    httpListener.Prefixes.Add($"http://{localIP}:{port}/");
                    System.Diagnostics.Debug.WriteLine($"🌐 إضافة عنوان IP المحلي: {localIP}:{port}");
                }

                httpListener.Start();
                isRunning = true;

                System.Diagnostics.Debug.WriteLine($"🌐 تم بدء خادم المزامنة على المنفذ {port}");
                System.Diagnostics.Debug.WriteLine($"📍 عنوان IP المحلي: {localIP ?? "غير متاح"}");

                // بدء استقبال الطلبات
                _ = Task.Run(async () => await ListenForRequestsAsync());

                // بدء البحث عن الأجهزة الأخرى مع تأخير أولي
                _ = Task.Run(async () =>
                {
                    await Task.Delay(2000); // انتظار ثانيتين قبل البدء
                    await DiscoverDevicesAsync();
                });

                // انتظار قصير للتأكد من بدء الخدمات
                await Task.Delay(100);
            }
            catch (HttpListenerException httpEx)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في HttpListener: {httpEx.Message}");
                System.Diagnostics.Debug.WriteLine($"💡 قد تحتاج إلى تشغيل البرنامج كمدير أو تعديل إعدادات جدار الحماية");

                // محاولة استخدام منفذ بديل
                await TryAlternativePort();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في بدء خادم المزامنة: {ex.Message}");
            }
        }

        private async Task TryAlternativePort()
        {
            try
            {
                // محاولة منافذ بديلة
                var alternatePorts = new[] { 8081, 8082, 8083, 9090 };

                foreach (var altPort in alternatePorts)
                {
                    try
                    {
                        httpListener = new HttpListener();
                        httpListener.Prefixes.Add($"http://localhost:{altPort}/");
                        httpListener.Start();

                        // تحديث المنفذ المستخدم
                        var oldPort = port;
                        typeof(NetworkSyncManager).GetField("port", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.SetValue(this, altPort);

                        isRunning = true;
                        System.Diagnostics.Debug.WriteLine($"✅ تم بدء الخادم على المنفذ البديل {altPort} بدلاً من {oldPort}");

                        // بدء الخدمات
                        _ = Task.Run(async () => await ListenForRequestsAsync());
                        _ = Task.Run(async () =>
                        {
                            await Task.Delay(2000);
                            await DiscoverDevicesAsync();
                        });

                        return;
                    }
                    catch
                    {
                        httpListener?.Close();
                        continue;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"❌ فشل في بدء الخادم على جميع المنافذ البديلة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في محاولة المنافذ البديلة: {ex.Message}");
            }
        }

        private async Task ListenForRequestsAsync()
        {
            while (isRunning && httpListener.IsListening)
            {
                try
                {
                    var context = await httpListener.GetContextAsync();
                    _ = Task.Run(() => ProcessRequestAsync(context));
                }
                catch (Exception ex)
                {
                    if (isRunning)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في استقبال الطلبات: {ex.Message}");
                    }
                }
            }
        }

        private async Task ProcessRequestAsync(HttpListenerContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                if (request.HttpMethod == "POST" && request.Url.AbsolutePath == "/sync-message")
                {
                    // استقبال رسالة جديدة للمزامنة
                    using (var reader = new StreamReader(request.InputStream))
                    {
                        var jsonData = await reader.ReadToEndAsync();
                        var messageData = JsonSerializer.Deserialize<SyncMessage>(jsonData);

                        if (messageData != null)
                        {
                            // إشعار النافذة الرئيسية بالرسالة الجديدة
                            MessageReceived?.Invoke(messageData.Sender, messageData.Content, messageData.Timestamp);

                            // إرسال رد نجح
                            var responseData = Encoding.UTF8.GetBytes("{\"status\":\"success\"}");
                            response.ContentType = "application/json";
                            response.ContentLength64 = responseData.Length;
                            await response.OutputStream.WriteAsync(responseData, 0, responseData.Length);
                        }
                    }
                }
                else if (request.HttpMethod == "POST" && request.Url.AbsolutePath == "/sync-data")
                {
                    // استقبال مزامنة البيانات الكاملة
                    using (var reader = new StreamReader(request.InputStream))
                    {
                        var jsonData = await reader.ReadToEndAsync();
                        var syncData = JsonSerializer.Deserialize<DataSyncMessage>(jsonData);

                        if (syncData != null && syncData.ContentItems != null)
                        {
                            // إشعار النافذة الرئيسية بالبيانات المحدثة
                            DataSyncReceived?.Invoke(syncData.ContentItems);

                            // إرسال رد نجح
                            var responseData = Encoding.UTF8.GetBytes("{\"status\":\"success\",\"synced_items\":" + syncData.ContentItems.Count + "}");
                            response.ContentType = "application/json";
                            response.ContentLength64 = responseData.Length;
                            await response.OutputStream.WriteAsync(responseData, 0, responseData.Length);
                        }
                    }
                }
                else if (request.HttpMethod == "POST" && request.Url.AbsolutePath == "/sync-settings")
                {
                    // استقبال مزامنة الإعدادات
                    using (var reader = new StreamReader(request.InputStream))
                    {
                        var jsonData = await reader.ReadToEndAsync();
                        var settingsData = JsonSerializer.Deserialize<SettingsSyncMessage>(jsonData);

                        if (settingsData != null)
                        {
                            // إشعار النافذة الرئيسية بالإعدادات المحدثة
                            SettingsUpdateReceived?.Invoke(settingsData.SettingKey, settingsData.SettingValue);

                            // إرسال رد نجح
                            var responseData = Encoding.UTF8.GetBytes("{\"status\":\"success\"}");
                            response.ContentType = "application/json";
                            response.ContentLength64 = responseData.Length;
                            await response.OutputStream.WriteAsync(responseData, 0, responseData.Length);
                        }
                    }
                }
                else if (request.HttpMethod == "GET" && request.Url.AbsolutePath == "/ping")
                {
                    // رد على ping للتحقق من الاتصال
                    var responseData = Encoding.UTF8.GetBytes("{\"status\":\"alive\",\"device\":\"InzoIB\"}");
                    response.ContentType = "application/json";
                    response.ContentLength64 = responseData.Length;
                    await response.OutputStream.WriteAsync(responseData, 0, responseData.Length);
                }

                response.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الطلب: {ex.Message}");
            }
        }

        private async Task DiscoverDevicesAsync()
        {
            System.Diagnostics.Debug.WriteLine("🔍 بدء البحث عن الأجهزة...");

            while (isRunning)
            {
                try
                {
                    // البحث عن الأجهزة في الشبكة المحلية
                    var localIP = GetLocalIPAddress();
                    System.Diagnostics.Debug.WriteLine($"📍 عنوان IP المحلي للبحث: {localIP}");

                    if (localIP != null)
                    {
                        var subnet = localIP.Substring(0, localIP.LastIndexOf('.'));
                        System.Diagnostics.Debug.WriteLine($"🌐 البحث في الشبكة الفرعية: {subnet}.x");

                        // البحث في نطاق أصغر أولاً للسرعة
                        var commonIPs = new List<string>();

                        // إضافة عناوين IP شائعة أولاً
                        for (int i = 1; i <= 20; i++)
                        {
                            commonIPs.Add($"{subnet}.{i}");
                        }
                        for (int i = 100; i <= 120; i++)
                        {
                            commonIPs.Add($"{subnet}.{i}");
                        }
                        for (int i = 200; i <= 220; i++)
                        {
                            commonIPs.Add($"{subnet}.{i}");
                        }

                        // فحص العناوين الشائعة أولاً
                        var tasks = new List<Task>();
                        foreach (var targetIP in commonIPs)
                        {
                            if (targetIP != localIP)
                            {
                                tasks.Add(CheckDeviceAsync(targetIP));
                            }
                        }

                        // انتظار انتهاء الفحص السريع
                        await Task.WhenAll(tasks);

                        // إذا لم نجد أجهزة، نبحث في النطاق الكامل
                        if (connectedDevices.Count == 0)
                        {
                            System.Diagnostics.Debug.WriteLine("🔍 لم يتم العثور على أجهزة في النطاق السريع، البحث في النطاق الكامل...");

                            tasks.Clear();
                            for (int i = 21; i <= 254; i++)
                            {
                                if (i >= 100 && i <= 120) continue; // تم فحصها بالفعل
                                if (i >= 200 && i <= 220) continue; // تم فحصها بالفعل

                                var targetIP = $"{subnet}.{i}";
                                if (targetIP != localIP)
                                {
                                    tasks.Add(CheckDeviceAsync(targetIP));

                                    // فحص 10 عناوين في كل مرة لتجنب الحمل الزائد
                                    if (tasks.Count >= 10)
                                    {
                                        await Task.WhenAll(tasks);
                                        tasks.Clear();
                                        await Task.Delay(100); // توقف قصير
                                    }
                                }
                            }

                            if (tasks.Count > 0)
                            {
                                await Task.WhenAll(tasks);
                            }
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("❌ لا يمكن الحصول على عنوان IP المحلي");
                    }

                    // البحث في الشبكات الخارجية
                    if (externalNetworks != null && externalNetworks.Count > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"🌐 البحث في {externalNetworks.Count} شبكة خارجية...");

                        var externalTasks = new List<Task>();
                        foreach (var network in externalNetworks)
                        {
                            externalTasks.Add(CheckDeviceAsync(network.IP));
                        }

                        await Task.WhenAll(externalTasks);
                        System.Diagnostics.Debug.WriteLine("✅ انتهى البحث في الشبكات الخارجية");
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ انتهى البحث. الأجهزة المتصلة: {connectedDevices.Count}");

                    // انتظار 30 ثانية قبل البحث مرة أخرى
                    await Task.Delay(30000);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في اكتشاف الأجهزة: {ex.Message}");
                    await Task.Delay(5000);
                }
            }
        }

        private async Task CheckDeviceAsync(string ip)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(3);

                    // محاولة عدة منافذ
                    var portsToTry = new[] { port, 8080, 8081, 8082, 8083, 9090 };

                    foreach (var portToTry in portsToTry)
                    {
                        try
                        {
                            var response = await client.GetAsync($"http://{ip}:{portToTry}/ping");

                            if (response.IsSuccessStatusCode)
                            {
                                var content = await response.Content.ReadAsStringAsync();
                                var pingResponse = JsonSerializer.Deserialize<PingResponse>(content);

                                if (pingResponse?.Device == "InzoIB")
                                {
                                    var deviceKey = $"{ip}:{portToTry}";
                                    if (!connectedDevices.Contains(deviceKey))
                                    {
                                        connectedDevices.Add(deviceKey);
                                        DeviceConnected?.Invoke(deviceKey);
                                        System.Diagnostics.Debug.WriteLine($"🔗 تم اكتشاف جهاز InzoIB على {deviceKey}");
                                    }
                                    return; // تم العثور على الجهاز، لا حاجة لفحص منافذ أخرى
                                }
                            }
                        }
                        catch
                        {
                            // تجاهل أخطاء المنافذ الفردية والمتابعة
                            continue;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل الأخطاء فقط للتشخيص
                if (ex.Message.Contains("timeout") || ex.Message.Contains("refused"))
                {
                    // أخطاء شائعة، لا حاجة لتسجيلها
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في فحص {ip}: {ex.Message}");
                }

                // إزالة الجهاز من القائمة إذا لم يعد متاحاً
                var devicesToRemove = connectedDevices.Where(d => d.StartsWith(ip + ":")).ToList();
                foreach (var device in devicesToRemove)
                {
                    connectedDevices.Remove(device);
                    DeviceDisconnected?.Invoke(device);
                    System.Diagnostics.Debug.WriteLine($"❌ انقطع الاتصال مع الجهاز {device}");
                }
            }
        }

        public async Task BroadcastMessageAsync(string sender, string content, DateTime timestamp)
        {
            var message = new SyncMessage
            {
                Sender = sender,
                Content = content,
                Timestamp = timestamp
            };

            var jsonData = JsonSerializer.Serialize(message);
            System.Diagnostics.Debug.WriteLine($"🌐 بدء إرسال الرسالة إلى {connectedDevices.Count} جهاز");

            foreach (var device in connectedDevices.ToList())
            {
                try
                {
                    using (var client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromSeconds(5);
                        var content_http = new StringContent(jsonData, Encoding.UTF8, "application/json");

                        // التعامل مع تنسيق العنوان الجديد (IP:Port)
                        string url;
                        if (device.Contains(":"))
                        {
                            url = $"http://{device}/sync-message";
                        }
                        else
                        {
                            url = $"http://{device}:{port}/sync-message";
                        }

                        var response = await client.PostAsync(url, content_http);

                        if (response.IsSuccessStatusCode)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ تم إرسال الرسالة إلى {device}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ فشل إرسال الرسالة إلى {device}: {response.StatusCode}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في إرسال الرسالة إلى {device}: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"✅ انتهى إرسال الرسالة إلى جميع الأجهزة");
        }

        // مزامنة إعداد واحد عبر الشبكة المحلية
        public async Task SyncSettingAsync(string settingKey, object settingValue)
        {
            var settingMessage = new SettingsUpdateMessage
            {
                SettingKey = settingKey,
                SettingValue = settingValue,
                Timestamp = DateTime.Now
            };

            var jsonData = JsonSerializer.Serialize(settingMessage);
            System.Diagnostics.Debug.WriteLine($"🔄 بدء مزامنة الإعداد {settingKey} مع {connectedDevices.Count} جهاز");

            foreach (var device in connectedDevices.ToList())
            {
                try
                {
                    using (var client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromSeconds(10);
                        var content_http = new StringContent(jsonData, Encoding.UTF8, "application/json");

                        string url;
                        if (device.Contains(":"))
                        {
                            url = $"http://{device}/sync-settings";
                        }
                        else
                        {
                            url = $"http://{device}:{port}/sync-settings";
                        }

                        var response = await client.PostAsync(url, content_http);

                        if (response.IsSuccessStatusCode)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ تم مزامنة الإعداد {settingKey} مع {device}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ فشل مزامنة الإعداد {settingKey} مع {device}: {response.StatusCode}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة الإعداد {settingKey} مع {device}: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"✅ انتهى مزامنة الإعداد {settingKey} مع جميع الأجهزة");
        }

        // دالة جديدة لمزامنة البيانات الكاملة
        public async Task BroadcastDataSyncAsync(List<ContentItem> contentItems)
        {
            var syncMessage = new DataSyncMessage
            {
                ContentItems = contentItems,
                Timestamp = DateTime.Now,
                SyncType = "FULL_DATA_SYNC"
            };

            var jsonData = JsonSerializer.Serialize(syncMessage);
            System.Diagnostics.Debug.WriteLine($"🔄 بدء مزامنة البيانات مع {connectedDevices.Count} جهاز ({contentItems.Count} عنصر)");

            foreach (var device in connectedDevices.ToList())
            {
                try
                {
                    using (var client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromSeconds(10);
                        var content_http = new StringContent(jsonData, Encoding.UTF8, "application/json");

                        string url;
                        if (device.Contains(":"))
                        {
                            url = $"http://{device}/sync-data";
                        }
                        else
                        {
                            url = $"http://{device}:{port}/sync-data";
                        }

                        var response = await client.PostAsync(url, content_http);

                        if (response.IsSuccessStatusCode)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ تم مزامنة البيانات مع {device}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ فشل مزامنة البيانات مع {device}: {response.StatusCode}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة البيانات مع {device}: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"✅ انتهت مزامنة البيانات مع جميع الأجهزة");
        }

        // دالة جديدة لمزامنة الإعدادات
        public async Task BroadcastSettingsUpdateAsync(string settingKey, object settingValue)
        {
            var settingsMessage = new SettingsSyncMessage
            {
                SettingKey = settingKey,
                SettingValue = settingValue,
                Timestamp = DateTime.Now
            };

            var jsonData = JsonSerializer.Serialize(settingsMessage);
            System.Diagnostics.Debug.WriteLine($"⚙️ بدء مزامنة الإعداد {settingKey} مع {connectedDevices.Count} جهاز");

            foreach (var device in connectedDevices.ToList())
            {
                try
                {
                    using (var client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromSeconds(5);
                        var content_http = new StringContent(jsonData, Encoding.UTF8, "application/json");

                        string url;
                        if (device.Contains(":"))
                        {
                            url = $"http://{device}/sync-settings";
                        }
                        else
                        {
                            url = $"http://{device}:{port}/sync-settings";
                        }

                        var response = await client.PostAsync(url, content_http);

                        if (response.IsSuccessStatusCode)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ تم مزامنة الإعداد مع {device}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ فشل مزامنة الإعداد مع {device}: {response.StatusCode}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة الإعداد مع {device}: {ex.Message}");
                }
            }

            System.Diagnostics.Debug.WriteLine($"✅ انتهت مزامنة الإعداد مع جميع الأجهزة");
        }

        private string GetLocalIPAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        return ip.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على عنوان IP: {ex.Message}");
            }
            return null;
        }

        public void Stop()
        {
            try
            {
                isRunning = false;
                httpListener?.Stop();
                httpListener?.Close();
                System.Diagnostics.Debug.WriteLine("🛑 تم إيقاف خادم المزامنة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إيقاف خادم المزامنة: {ex.Message}");
            }
        }

        public List<string> GetConnectedDevices() => new List<string>(connectedDevices);

        public async Task QuickDeviceSearch()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 بدء البحث السريع عن الأجهزة...");

                var localIP = GetLocalIPAddress();
                if (localIP != null)
                {
                    var subnet = localIP.Substring(0, localIP.LastIndexOf('.'));

                    // البحث في العناوين الأكثر شيوعاً
                    var quickIPs = new List<string>();
                    for (int i = 1; i <= 10; i++)
                    {
                        quickIPs.Add($"{subnet}.{i}");
                    }
                    quickIPs.AddRange(new[] {
                        $"{subnet}.100", $"{subnet}.101", $"{subnet}.102",
                        $"{subnet}.200", $"{subnet}.201", $"{subnet}.202"
                    });

                    var tasks = quickIPs.Where(ip => ip != localIP)
                                       .Select(ip => CheckDeviceAsync(ip))
                                       .ToArray();

                    await Task.WhenAll(tasks);

                    System.Diagnostics.Debug.WriteLine($"✅ انتهى البحث السريع. تم العثور على {connectedDevices.Count} جهاز");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث السريع: {ex.Message}");
            }
        }

        public async Task<List<string>> TestServerAsync()
        {
            var results = new List<string>();

            try
            {
                if (isRunning)
                {
                    results.Add("✅ خادم المزامنة يعمل");

                    // اختبار الخادم المحلي
                    try
                    {
                        using (var client = new HttpClient())
                        {
                            client.Timeout = TimeSpan.FromSeconds(5);
                            var response = await client.GetAsync($"http://localhost:{port}/ping");
                            if (response.IsSuccessStatusCode)
                            {
                                results.Add($"✅ الخادم المحلي يستجيب على المنفذ {port}");
                            }
                            else
                            {
                                results.Add($"❌ الخادم المحلي لا يستجيب على المنفذ {port}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        results.Add($"❌ خطأ في اختبار الخادم المحلي: {ex.Message}");
                    }

                    // فحص جدار الحماية
                    var localIP = GetLocalIPAddress();
                    if (localIP != null)
                    {
                        try
                        {
                            using (var client = new HttpClient())
                            {
                                client.Timeout = TimeSpan.FromSeconds(3);
                                var response = await client.GetAsync($"http://{localIP}:{port}/ping");
                                if (response.IsSuccessStatusCode)
                                {
                                    results.Add("✅ جدار الحماية يسمح بالاتصالات");
                                }
                                else
                                {
                                    results.Add("⚠️ قد يكون جدار الحماية يحجب الاتصالات");
                                }
                            }
                        }
                        catch
                        {
                            results.Add("❌ جدار الحماية يحجب الاتصالات أو مشكلة في الشبكة");
                        }
                    }
                }
                else
                {
                    results.Add("❌ خادم المزامنة غير مفعل");
                }
            }
            catch (Exception ex)
            {
                results.Add($"❌ خطأ في اختبار الخادم: {ex.Message}");
            }

            return results;
        }
    }

    // فئات البيانات للمزامنة
    public class SyncMessage
    {
        public string Sender { get; set; }
        public string Content { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class DataSyncMessage
    {
        public List<ContentItem> ContentItems { get; set; }
        public DateTime Timestamp { get; set; }
        public string SyncType { get; set; }
    }

    public class SettingsSyncMessage
    {
        public string SettingKey { get; set; }
        public object SettingValue { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class PingResponse
    {
        public string Status { get; set; }
        public string Device { get; set; }
    }

    // فئة لحفظ الرسائل المعلقة
    public class PendingMessage
    {
        public string Sender { get; set; }
        public string Content { get; set; }
        public DateTime Timestamp { get; set; }
        public long ChatId { get; set; }
        public string ChatTitle { get; set; }
        public string ChatType { get; set; }
        public bool IsFromGroupOrChannel { get; set; }
    }

    public partial class MainWindow : Window
    {
        #region Windows API للتحكم في شريط العنوان

        [DllImport("dwmapi.dll")]
        private static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);

        [DllImport("dwmapi.dll")]
        private static extern int DwmIsCompositionEnabled(out bool enabled);

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        private static extern int SetWindowLong(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        private const int GWL_EXSTYLE = -20;
        private const int WS_EX_DLGMODALFRAME = 0x0001;
        private const int SWP_NOSIZE = 0x0001;
        private const int SWP_NOMOVE = 0x0002;
        private const int SWP_NOZORDER = 0x0004;
        private const int SWP_FRAMECHANGED = 0x0020;
        private const int DWMWA_CAPTION_COLOR = 35;
        private const int DWMWA_USE_IMMERSIVE_DARK_MODE = 20;

        #endregion
        private DispatcherTimer timer;
        private string currentSection = "توضيح";
        private List<ContentItem> contentItems;
        private ContentItem selectedItem;

        // مسار حفظ البيانات
        private readonly string dataFolderPath;
        private readonly string dataFilePath;

        // Network Configuration
        private static readonly HttpClient httpClient = new HttpClient();
        private bool isInternetConnected = false;
        private DispatcherTimer networkCheckTimer;

        // OpenAI Configuration
        private ChatClient openAIClient;
        private string openAIApiKey = "YOUR_OPENAI_API_KEY_HERE"; // سيتم تحميله من الملف
        private readonly string apiKeyFilePath;

        // Telegram Bot Configuration
        private TelegramBotClient telegramBot;
        private string telegramBotToken = ""; // سيتم تحميله من الملف
        private readonly string botTokenFilePath;
        private CancellationTokenSource telegramCancellationToken;

        // Network Sync Configuration
        private NetworkSyncManager networkSyncManager;
        private List<ExternalIPInfo> externalNetworks = new List<ExternalIPInfo>();

        // Firestore Configuration
        private FirestoreManager firestoreManager;
        private readonly string firestoreSettingsFilePath;
        private bool useFirestore = false; // تحديد ما إذا كان سيتم استخدام Firestore أم JSON المحلي

        // Universal Sync Manager - مدير المزامنة الشامل
        private UniversalSyncManager universalSyncManager;

        // Pending Messages tracking
        private readonly string pendingMessagesFilePath;
        private readonly List<PendingMessage> pendingMessages = new List<PendingMessage>();

        // Message tracking for deletion
        private readonly Dictionary<string, ContentItem> telegramMessageMap = new Dictionary<string, ContentItem>();

        // Timer for checking deleted messages
        private DispatcherTimer deletedMessageCheckTimer;

        public MainWindow()
        {
            try
            {
                // تهيئة مسارات الحفظ (نسبي للنقل بين الأجهزة)
                // استخدام مسار نسبي بجوار ملف البرنامج
                var executablePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                var executableDirectory = Path.GetDirectoryName(executablePath);
                dataFolderPath = Path.Combine(executableDirectory, "Data");
                dataFilePath = Path.Combine(dataFolderPath, "content_data.json");
                apiKeyFilePath = Path.Combine(dataFolderPath, "api_key.dat");
                botTokenFilePath = Path.Combine(dataFolderPath, "bot_token.dat");
                pendingMessagesFilePath = Path.Combine(dataFolderPath, "pending_messages.json");
                firestoreSettingsFilePath = Path.Combine(dataFolderPath, "firestore_settings.dat");

                System.Diagnostics.Debug.WriteLine($"مجلد البرنامج: {executableDirectory}");
                System.Diagnostics.Debug.WriteLine($"مجلد البيانات: {dataFolderPath}");
                System.Diagnostics.Debug.WriteLine($"ملف البيانات: {dataFilePath}");

                // إنشاء مجلد البيانات إذا لم يكن موجوداً
                if (!Directory.Exists(dataFolderPath))
                {
                    Directory.CreateDirectory(dataFolderPath);
                    System.Diagnostics.Debug.WriteLine("تم إنشاء مجلد البيانات");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("مجلد البيانات موجود بالفعل");
                }

                // تهيئة المكونات الأساسية أولاً
                InitializeComponent();

                // إضافة معالجات الأحداث
                this.Loaded += MainWindow_Loaded;
                this.Activated += MainWindow_Activated;
                this.Closing += MainWindow_Closing;

                // تهيئة البيانات
                InitializeContentItems();

                // تهيئة الشبكة
                InitializeNetwork();

                // تحميل API key وتهيئة OpenAI
                LoadApiKey();
                InitializeOpenAI();

                // تحميل توكن البوت
                LoadBotToken();

                // تحميل الرسائل المعلقة
                LoadPendingMessages();

                // إعادة بناء قاموس تتبع الرسائل
                RebuildTelegramMessageMap();

                // تهيئة بوت التيليجرام
                InitializeTelegramBot();

                // تهيئة مؤقت فحص الرسائل المحذوفة
                InitializeDeletedMessageChecker();

                // تحميل إعدادات الشبكات الخارجية
                LoadExternalNetworks();

                // تهيئة مدير Firestore
                InitializeFirestore();

                // تهيئة مدير المزامنة عبر الشبكة
                InitializeNetworkSync();

                // تهيئة مدير المزامنة الشامل
                InitializeUniversalSync();

                // بدء المؤقت
                StartTimer();

                // تطبيق لون شريط العنوان بطرق متعددة
                this.Loaded += (s, e) => ApplyTitleBarColor();
                this.Activated += (s, e) => ApplyTitleBarColor();
                this.SourceInitialized += (s, e) => ApplyTitleBarColor();

                // إنشاء ملف README للنقل بين الأجهزة (معطل)
                // CreatePortabilityReadme();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                    "خطأ في التهيئة", MessageBoxButton.OK, MessageBoxImage.Error);

                // محاولة إغلاق التطبيق بأمان
                Application.Current?.Shutdown();
            }
        }

        #region دالة شريط العنوان المنفصلة

        /// <summary>
        /// دالة منفصلة لتطبيق لون شريط العنوان
        /// تستخدم نفس لون زر التوضيح (#161642)
        /// محسنة للعمل على جميع الأجهزة وإصدارات Windows
        /// </summary>
        private void ApplyTitleBarColor()
        {
            try
            {
                var hwnd = new WindowInteropHelper(this).Handle;
                if (hwnd == IntPtr.Zero)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم الحصول على handle النافذة");
                    return;
                }

                // التحقق من دعم النظام لميزات شريط العنوان
                bool isWindows10OrLater = IsWindows10OrLater();
                bool supportsTitleBar = SupportsTitleBarCustomization();
                bool isCompositionEnabled = false;

                try
                {
                    DwmIsCompositionEnabled(out isCompositionEnabled);
                }
                catch
                {
                    isCompositionEnabled = false;
                }

                System.Diagnostics.Debug.WriteLine($"📊 فحص دعم النظام:");
                System.Diagnostics.Debug.WriteLine($"   - Windows 10 أو أحدث: {isWindows10OrLater}");
                System.Diagnostics.Debug.WriteLine($"   - دعم تخصيص شريط العنوان: {supportsTitleBar}");
                System.Diagnostics.Debug.WriteLine($"   - DWM Composition مفعل: {isCompositionEnabled}");

                if (!isWindows10OrLater || !supportsTitleBar)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ النظام لا يدعم تخصيص شريط العنوان المتقدم");
                }

                // محاولة تطبيق اللون بطرق متعددة
                bool colorApplied = false;

                // الطريقة الأولى: استخدام DWMWA_CAPTION_COLOR (Windows 11)
                try
                {
                    int titleBarColor = 0x421616; // BGR format for #161642
                    int result = DwmSetWindowAttribute(hwnd, DWMWA_CAPTION_COLOR, ref titleBarColor, sizeof(int));
                    if (result == 0)
                    {
                        colorApplied = true;
                        System.Diagnostics.Debug.WriteLine("✅ تم تطبيق لون شريط العنوان بالطريقة الأولى (CAPTION_COLOR)");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ فشل في الطريقة الأولى، كود الخطأ: {result}");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في الطريقة الأولى: {ex.Message}");
                }

                // الطريقة الثانية: استخدام Dark Mode (Windows 10/11)
                if (!colorApplied)
                {
                    try
                    {
                        int darkMode = 1; // تفعيل الوضع الداكن
                        int result = DwmSetWindowAttribute(hwnd, DWMWA_USE_IMMERSIVE_DARK_MODE, ref darkMode, sizeof(int));
                        if (result == 0)
                        {
                            colorApplied = true;
                            System.Diagnostics.Debug.WriteLine("✅ تم تطبيق الوضع الداكن لشريط العنوان");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ فشل في تطبيق الوضع الداكن، كود الخطأ: {result}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تطبيق الوضع الداكن: {ex.Message}");
                    }
                }

                // الطريقة الثالثة: تطبيق لون بديل للنافذة نفسها
                if (!colorApplied)
                {
                    try
                    {
                        // تطبيق لون خلفية النافذة
                        this.Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42));
                        System.Diagnostics.Debug.WriteLine("✅ تم تطبيق لون خلفية النافذة كبديل");
                        colorApplied = true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تطبيق لون خلفية النافذة: {ex.Message}");
                    }
                }

                // تقرير النتيجة النهائية
                if (colorApplied)
                {
                    System.Diagnostics.Debug.WriteLine("🎨 تم تطبيق لون شريط العنوان بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل في تطبيق لون شريط العنوان بجميع الطرق");
                }

                // معلومات إضافية للتشخيص
                System.Diagnostics.Debug.WriteLine($"📊 معلومات النظام:");
                System.Diagnostics.Debug.WriteLine($"   - إصدار Windows: {Environment.OSVersion}");
                System.Diagnostics.Debug.WriteLine($"   - Handle النافذة: {hwnd}");
                System.Diagnostics.Debug.WriteLine($"   - DWM مفعل: {isCompositionEnabled}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في تطبيق لون شريط العنوان: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"   Stack Trace: {ex.StackTrace}");
            }
        }

        #endregion

        #region دالة التحقق من إصدار Windows

        /// <summary>
        /// دالة للتحقق من إصدار Windows ودعم ميزات شريط العنوان
        /// </summary>
        private bool IsWindows10OrLater()
        {
            try
            {
                var version = Environment.OSVersion.Version;
                // Windows 10 = 10.0, Windows 11 = 10.0 (Build 22000+)
                return version.Major >= 10;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// دالة للتحقق من دعم ميزات شريط العنوان المتقدمة
        /// </summary>
        private bool SupportsTitleBarCustomization()
        {
            try
            {
                // التحقق من وجود dwmapi.dll
                var dwmModule = GetModuleHandle("dwmapi.dll");
                if (dwmModule == IntPtr.Zero)
                {
                    return false;
                }

                // التحقق من وجود الدالة المطلوبة
                var procAddress = GetProcAddress(dwmModule, "DwmSetWindowAttribute");
                return procAddress != IntPtr.Zero;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region دالة إنشاء ملف README للنقل بين الأجهزة

        /// <summary>
        /// دالة منفصلة لإنشاء ملف README يوضح كيفية نقل البرنامج بين الأجهزة
        /// </summary>
        private void CreatePortabilityReadme()
        {
            try
            {
                var executablePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
                var executableDirectory = Path.GetDirectoryName(executablePath);
                var readmePath = Path.Combine(executableDirectory, "نقل البرنامج بين الأجهزة.txt");

                if (!File.Exists(readmePath))
                {
                    var readmeContent = @"🎯 Inzo IB v7.4 - دليل نقل البرنامج بين الأجهزة
===============================================

📁 هيكل ملفات البرنامج:
├── InzoIB_v7.4_Simple.exe          # ملف البرنامج الرئيسي
├── InzoIB_v7.4_Simple.dll          # مكتبات البرنامج
├── Data/                           # مجلد البيانات (يحتوي على جميع المحتويات)
│   ├── content_data.json           # ملف البيانات الرئيسي
│   └── Attachments/                # مجلد الملفات المرفقة
│       ├── 20241215120000_file1.pdf
│       ├── 20241215120001_image1.jpg
│       └── ...
├── نقل البرنامج بين الأجهزة.txt      # هذا الملف
└── ملفات أخرى...

🔄 كيفية نقل البرنامج إلى جهاز آخر:

1️⃣ نسخ المجلد كاملاً:
   • انسخ المجلد الذي يحتوي على البرنامج بالكامل
   • تأكد من نسخ مجلد ""Data"" مع جميع محتوياته
   • تأكد من نسخ مجلد ""Attachments"" مع جميع الملفات المرفقة

2️⃣ لصق المجلد في الجهاز الجديد:
   • الصق المجلد في أي مكان تريده في الجهاز الجديد
   • لا تحتاج لتثبيت أو إعداد إضافي

3️⃣ تشغيل البرنامج:
   • انقر مرتين على InzoIB_v7.4_Simple.exe
   • سيتم تحميل جميع البيانات والملفات تلقائياً

✅ مميزات النقل:
• البرنامج محمول (Portable) - لا يحتاج تثبيت
• جميع البيانات محفوظة في مجلد البرنامج
• الملفات المرفقة محفوظة محلياً
• لا يعتمد على مسارات مطلقة
• يعمل على أي جهاز Windows

⚠️ ملاحظات مهمة:
• تأكد من نسخ مجلد ""Data"" كاملاً
• لا تحذف أو تنقل ملفات من مجلد ""Attachments""
• احتفظ بنسخة احتياطية من مجلد البرنامج
• البرنامج يحفظ البيانات تلقائياً عند كل تغيير

🎊 استمتع باستخدام البرنامج على أي جهاز!

تاريخ الإنشاء: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + @"
إصدار البرنامج: Inzo IB v7.4";

                    File.WriteAllText(readmePath, readmeContent, System.Text.Encoding.UTF8);
                    System.Diagnostics.Debug.WriteLine($"تم إنشاء ملف README في: {readmePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء ملف README: {ex.Message}");
            }
        }

        #endregion

        #region دالة التحقق من سلامة البيانات عند النقل

        /// <summary>
        /// دالة منفصلة للتحقق من سلامة البيانات والملفات المرفقة
        /// تضمن عمل البرنامج بشكل صحيح بعد النقل بين الأجهزة
        /// </summary>
        private void ValidateDataIntegrity()
        {
            try
            {
                if (contentItems == null || contentItems.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("لا توجد بيانات للتحقق منها");
                    return;
                }

                int validFiles = 0;
                int invalidFiles = 0;
                int totalFiles = 0;

                foreach (var item in contentItems)
                {
                    if (item.AttachedFiles != null && item.AttachedFiles.Count > 0)
                    {
                        for (int i = item.AttachedFiles.Count - 1; i >= 0; i--)
                        {
                            totalFiles++;
                            var filePath = item.AttachedFiles[i];

                            // التحقق من وجود الملف
                            if (File.Exists(filePath))
                            {
                                validFiles++;
                                System.Diagnostics.Debug.WriteLine($"ملف صالح: {Path.GetFileName(filePath)}");
                            }
                            else
                            {
                                invalidFiles++;
                                System.Diagnostics.Debug.WriteLine($"ملف مفقود: {Path.GetFileName(filePath)}");

                                // محاولة البحث عن الملف في مجلد Attachments
                                var fileName = Path.GetFileName(filePath);
                                var attachmentsFolder = Path.Combine(dataFolderPath, "Attachments");
                                var possiblePath = Path.Combine(attachmentsFolder, fileName);

                                if (File.Exists(possiblePath))
                                {
                                    // تحديث المسار إلى المسار الصحيح
                                    item.AttachedFiles[i] = possiblePath;
                                    validFiles++;
                                    invalidFiles--;
                                    System.Diagnostics.Debug.WriteLine($"تم إصلاح مسار الملف: {fileName}");
                                }
                            }
                        }
                    }
                }

                // إحصائيات التحقق
                System.Diagnostics.Debug.WriteLine($"=== تقرير سلامة البيانات ===");
                System.Diagnostics.Debug.WriteLine($"إجمالي العناصر: {contentItems.Count}");
                System.Diagnostics.Debug.WriteLine($"إجمالي الملفات: {totalFiles}");
                System.Diagnostics.Debug.WriteLine($"ملفات صالحة: {validFiles}");
                System.Diagnostics.Debug.WriteLine($"ملفات مفقودة: {invalidFiles}");

                if (invalidFiles > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ تحذير: {invalidFiles} ملف مفقود");
                }
                else if (totalFiles > 0)
                {
                    System.Diagnostics.Debug.WriteLine("✅ جميع الملفات سليمة");
                }

                // حفظ البيانات المحدثة إذا تم إصلاح أي مسارات
                if (validFiles > 0)
                {
                    SaveData();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من سلامة البيانات: {ex.Message}");
            }
        }

        #endregion

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحميل المحتوى بعد تحميل النافذة
                LoadCurrentSectionContent();

                // تعيين لون زر القسم الافتراضي (توضيح)
                UpdateSectionButtonColors(BtnTawdeeh);

                // عرض رسالة النجاح
                if (StatusText != null)
                {
                    StatusText.Text = "✅ تم تشغيل Inzo IB v7.4 بنجاح";
                }

                // التأكد من ظهور النافذة
                this.WindowState = WindowState.Normal;
                this.Activate();
                this.Focus();

                // جعل النافذة في المقدمة مؤقتاً
                this.Topmost = true;
                this.Topmost = false;

                // التأكد من إظهار جميع أزرار المهام وإعطائها أولوية عالية
                if (TaskEditButton != null)
                {
                    TaskEditButton.Visibility = Visibility.Visible;
                    TaskEditButton.IsEnabled = true;
                    Panel.SetZIndex(TaskEditButton, 1000);
                }
                if (TaskCopyButton != null)
                {
                    TaskCopyButton.Visibility = Visibility.Visible;
                    TaskCopyButton.IsEnabled = true;
                    Panel.SetZIndex(TaskCopyButton, 1000);
                }
                if (TaskDeleteButton != null)
                {
                    TaskDeleteButton.Visibility = Visibility.Visible;
                    TaskDeleteButton.IsEnabled = true;
                    Panel.SetZIndex(TaskDeleteButton, 1000);
                }
                if (TaskFilesButton != null)
                {
                    TaskFilesButton.Visibility = Visibility.Visible;
                    TaskFilesButton.IsEnabled = true;
                    Panel.SetZIndex(TaskFilesButton, 1000);
                }
                if (TaskMoveUpButton != null)
                {
                    TaskMoveUpButton.Visibility = Visibility.Visible;
                    TaskMoveUpButton.IsEnabled = true;
                    Panel.SetZIndex(TaskMoveUpButton, 1000);
                }
                if (TaskMoveDownButton != null)
                {
                    TaskMoveDownButton.Visibility = Visibility.Visible;
                    TaskMoveDownButton.IsEnabled = true;
                    Panel.SetZIndex(TaskMoveDownButton, 1000);
                }
                if (TaskDividerButton != null)
                {
                    TaskDividerButton.Visibility = Visibility.Visible;
                    TaskDividerButton.IsEnabled = true;
                    Panel.SetZIndex(TaskDividerButton, 1000);
                }
                if (TaskAddButton != null)
                {
                    TaskAddButton.Visibility = Visibility.Visible;
                    TaskAddButton.IsEnabled = true;
                    Panel.SetZIndex(TaskAddButton, 1000);
                }

                // فرض إظهار الأزرار مرة أخرى
                ForceShowTaskButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل النافذة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void MainWindow_Activated(object sender, EventArgs e)
        {
            try
            {
                // التأكد من أن النافذة مرئية
                this.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء البسيطة في التفعيل
                System.Diagnostics.Debug.WriteLine($"خطأ في تفعيل النافذة: {ex.Message}");
            }
        }

        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // حفظ البيانات قبل إغلاق البرنامج
                SaveData();

                // إيقاف المؤقت
                if (timer != null)
                {
                    timer.Stop();
                    timer = null;
                }

                // إيقاف مؤقت فحص الشبكة
                if (networkCheckTimer != null)
                {
                    networkCheckTimer.Stop();
                    networkCheckTimer = null;
                }

                // تنظيف موارد HttpClient
                httpClient?.Dispose();

                // إيقاف بوت التيليجرام
                if (telegramCancellationToken != null)
                {
                    telegramCancellationToken.Cancel();
                    telegramCancellationToken.Dispose();
                }

                // إيقاف مدير المزامنة عبر الشبكة
                if (networkSyncManager != null)
                {
                    networkSyncManager.Stop();
                    networkSyncManager = null;
                }

                // رسالة تأكيد الحفظ
                System.Diagnostics.Debug.WriteLine("تم حفظ جميع البيانات وتنظيف الموارد بنجاح قبل إغلاق البرنامج");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ البيانات عند الإغلاق: {ex.Message}");
            }
        }

        private void ForceShowTaskButtons()
        {
            try
            {
                // فرض إظهار جميع أزرار المهام
                var buttons = new[] { TaskEditButton, TaskCopyButton, TaskDeleteButton, TaskFilesButton,
                                     TaskMoveUpButton, TaskMoveDownButton, TaskDividerButton, TaskAddButton };

                foreach (var button in buttons)
                {
                    if (button != null)
                    {
                        button.Visibility = Visibility.Visible;
                        button.IsEnabled = true;
                        Panel.SetZIndex(button, 1000);
                        button.BringIntoView();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فرض إظهار الأزرار: {ex.Message}");
            }
        }

        private void InitializeContentItems()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء تهيئة البيانات...");

                // محاولة تحميل البيانات المحفوظة أولاً
                LoadData();

                System.Diagnostics.Debug.WriteLine($"📊 تم تحميل {contentItems?.Count ?? 0} عنصر");

                // التحقق من سلامة البيانات والملفات المرفقة
                ValidateDataIntegrity();

                // إذا لم تكن هناك بيانات محفوظة، إنشاء بيانات افتراضية
                if (contentItems == null || contentItems.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد بيانات محفوظة - إنشاء بيانات افتراضية");

                    contentItems = new List<ContentItem>
                    {
                        new ContentItem
                        {
                            Section = "توضيح",
                            Title = "مرحباً بك في Inzo IB v7.4",
                            Content = "نظام إدارة المحتوى المتطور مع واجهة عربية كاملة\n\n🎯 الميزات الرئيسية:\n• شريط مهام محسن مع أدوات متقدمة\n• بحث سريع وذكي\n• إدارة ملفات متطورة\n• واجهة سهلة الاستخدام",
                            CreatedDate = DateTime.Now
                        },
                        new ContentItem
                        {
                            Section = "توضيح",
                            Title = "الميزات المتوفرة",
                            Content = "📋 الميزات المتوفرة:\n• 13 قسم للمحتوى مع أيقونات\n• بحث فوري عبر جميع المحتوى\n• إضافة وتعديل وحذف المحتوى\n• واجهة عربية مع دعم RTL\n• حفظ تلقائي للبيانات",
                            CreatedDate = DateTime.Now
                        }
                    };

                    // حفظ البيانات الافتراضية
                    SaveData();
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء وحفظ البيانات الافتراضية");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل البيانات المحفوظة بنجاح: {contentItems.Count} عنصر");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة البيانات: {ex.Message}");

                // في حالة الخطأ، إنشاء قائمة فارغة
                contentItems = new List<ContentItem>();
            }
        }

        private void StartTimer()
        {
            try
            {
                timer = new DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(1);
                timer.Tick += (s, e) =>
                {
                    if (TimeText != null)
                    {
                        TimeText.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
                    }
                };
                timer.Start();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في بدء المؤقت: {ex.Message}");
            }
        }

        private void NavigateToSection(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null)
            {
                string sectionName = button.Content.ToString().Substring(2); // Remove emoji
                currentSection = sectionName.Trim();

                System.Diagnostics.Debug.WriteLine($"🔄 الانتقال إلى قسم: '{currentSection}'");
                StatusText.Text = $"تم الانتقال إلى قسم: {currentSection}";

                // تحديث ألوان أزرار الأقسام
                UpdateSectionButtonColors(button);

                // التمرير إلى الأعلى قبل تحميل المحتوى
                ContentScrollViewer.ScrollToTop();

                // تشخيص خاص لقسم Inzo IB
                if (currentSection == "INZO IB")
                {
                    System.Diagnostics.Debug.WriteLine("🔍 دخول قسم INZO IB - فحص رسائل التيليجرام");
                    var telegramMessages = contentItems.Where(item => item.Section == "Inzo IB").ToList();
                    System.Diagnostics.Debug.WriteLine($"📊 عدد رسائل التيليجرام الموجودة: {telegramMessages.Count}");

                    foreach (var msg in telegramMessages.Take(3)) // أول 3 رسائل للتشخيص
                    {
                        System.Diagnostics.Debug.WriteLine($"📝 رسالة: {msg.Title} - {msg.CreatedDate}");
                    }
                }

                // Update content based on section
                LoadCurrentSectionContent();
            }
        }

        #region Network Integration

        private void InitializeNetwork()
        {
            try
            {
                // تكوين HttpClient
                httpClient.Timeout = TimeSpan.FromSeconds(30);
                httpClient.DefaultRequestHeaders.Add("User-Agent", "InzoIB/7.4");

                // فحص الاتصال الأولي
                _ = Task.Run(async () => await CheckInternetConnection());

                // بدء مؤقت فحص الشبكة كل 30 ثانية
                networkCheckTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(30)
                };
                networkCheckTimer.Tick += NetworkCheckTimer_Tick;
                networkCheckTimer.Start();

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة الشبكة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة الشبكة: {ex.Message}");
            }
        }

        private async void NetworkCheckTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                await CheckInternetConnection();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص الشبكة الدوري: {ex.Message}");
            }
        }

        private async Task<bool> CheckInternetConnection()
        {
            try
            {
                // فحص الاتصال بـ Google DNS
                using (var ping = new Ping())
                {
                    var reply = await ping.SendPingAsync("*******", 5000);
                    isInternetConnected = reply.Status == IPStatus.Success;
                }

                // تحديث شريط الحالة
                UpdateNetworkStatus();

                return isInternetConnected;
            }
            catch (Exception ex)
            {
                isInternetConnected = false;
                UpdateNetworkStatus();
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص الاتصال: {ex.Message}");
                return false;
            }
        }

        private void UpdateNetworkStatus()
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    var networkStatus = isInternetConnected ? "🌐 متصل" : "🔴 غير متصل";
                    var currentStatus = StatusText.Text;

                    // إضافة حالة الشبكة إلى شريط الحالة
                    if (!currentStatus.Contains("🌐") && !currentStatus.Contains("🔴"))
                    {
                        StatusText.Text = $"{currentStatus} | {networkStatus}";
                    }
                    else
                    {
                        // استبدال حالة الشبكة الحالية
                        var parts = currentStatus.Split('|');
                        if (parts.Length > 1)
                        {
                            StatusText.Text = $"{parts[0].Trim()} | {networkStatus}";
                        }
                        else
                        {
                            StatusText.Text = $"{currentStatus} | {networkStatus}";
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حالة الشبكة: {ex.Message}");
            }
        }

        public async Task<string> FetchDataFromUrl(string url)
        {
            try
            {
                if (!isInternetConnected)
                {
                    return "❌ لا يوجد اتصال بالإنترنت";
                }

                var response = await httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                return content;
            }
            catch (HttpRequestException ex)
            {
                return $"❌ خطأ في الشبكة: {ex.Message}";
            }
            catch (TaskCanceledException)
            {
                return "❌ انتهت مهلة الاتصال";
            }
            catch (Exception ex)
            {
                return $"❌ خطأ غير متوقع: {ex.Message}";
            }
        }

        public async Task<T> FetchJsonFromUrl<T>(string url) where T : class
        {
            try
            {
                var jsonContent = await FetchDataFromUrl(url);

                if (jsonContent.StartsWith("❌"))
                {
                    return null;
                }

                return System.Text.Json.JsonSerializer.Deserialize<T>(jsonContent);
            }
            catch (System.Text.Json.JsonException ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحليل JSON: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب JSON: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> PostDataToUrl(string url, object data)
        {
            try
            {
                if (!isInternetConnected)
                {
                    return false;
                }

                var jsonContent = System.Text.Json.JsonSerializer.Serialize(data);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync(url, content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال البيانات: {ex.Message}");
                return false;
            }
        }

        public bool IsInternetConnected => isInternetConnected;



        private void ShowApiKeyDialog()
        {
            // استخدام النافذة البديلة البسيطة مباشرة لتجنب أي مشاكل
            ShowSimpleApiKeyDialog();
        }

        private void ShowSimpleApiKeyDialog()
        {
            try
            {
                // فتح نافذة إدخال API مباشرة بدون رسالة منبثقة
                var apiKeyDialog = new SimpleApiKeyDialog(openAIApiKey);
                apiKeyDialog.Owner = this;

                if (apiKeyDialog.ShowDialog() == true)
                {
                    var newApiKey = apiKeyDialog.ApiKey;

                    if (!string.IsNullOrEmpty(newApiKey))
                    {
                        openAIApiKey = newApiKey;

                        // حفظ المفتاح
                        SaveApiKey(newApiKey);

                        // إعادة تهيئة OpenAI
                        InitializeOpenAI();

                        StatusText.Text = "✅ تم حفظ مفتاح OpenAI GPT-4o! يمكنك الآن استخدام البحث الذكي المتقدم";

                        MessageBox.Show("✅ تم حفظ مفتاح OpenAI GPT-4o بنجاح!\n\n🤖 يمكنك الآن استخدام البحث الذكي المتقدم في البرنامج.\n\n⚡ مميزات GPT-4o:\n• تحليل أعمق وأذكى للمحتوى\n• إجابات أكثر دقة وتفصيلاً\n• فهم متقدم للغة العربية\n\n🔍 جرب البحث عن أي موضوع وستحصل على ردود ذكية متطورة!",
                            "تم الحفظ بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    StatusText.Text = "ℹ️ تم إلغاء إعدادات API";
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "❌ خطأ في إعدادات API";
                MessageBox.Show($"❌ خطأ في إعدادات API:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"خطأ في ShowSimpleApiKeyDialog: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }





        /// <summary>
        /// معالج النقر على زر الإعدادات الرئيسي
        /// </summary>
        private void SettingsMenuButton_Click(object sender, RoutedEventArgs e)
        {
            // إظهار أو إخفاء القائمة المنسدلة
            SettingsPopup.IsOpen = !SettingsPopup.IsOpen;
        }

        /// <summary>
        /// معالج النقر على زر إعدادات البوت
        /// </summary>
        private void BotSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق القائمة المنسدلة
            SettingsPopup.IsOpen = false;

            // إظهار نافذة إعدادات البوت
            ShowBotTokenDialog();
        }

        /// <summary>
        /// معالج النقر على زر API Key
        /// </summary>
        private void ApiKeyButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق القائمة المنسدلة
            SettingsPopup.IsOpen = false;

            // إظهار نافذة إعدادات API Key
            ShowApiKeyDialog();
        }

        /// <summary>
        /// معالج النقر على زر الشبكات الخارجية
        /// </summary>
        private void ExternalNetworkButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق القائمة المنسدلة
            SettingsPopup.IsOpen = false;

            // إظهار نافذة إعدادات الشبكة الخارجية
            ShowExternalNetworkDialog();
        }

        /// <summary>
        /// معالج النقر على زر معلومات IP
        /// </summary>
        private void IPInfoButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق القائمة المنسدلة
            SettingsPopup.IsOpen = false;

            // إظهار معلومات IP
            ShowIPInfo();
        }

        /// <summary>
        /// معالج النقر على زر إعدادات Firestore
        /// </summary>
        private void FirestoreSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق القائمة المنسدلة
            SettingsPopup.IsOpen = false;

            // إظهار نافذة إعدادات Firestore
            ShowFirestoreSettings();
        }

        /// <summary>
        /// إظهار نافذة إعدادات الشبكة الخارجية
        /// </summary>
        private void ShowExternalNetworkDialog()
        {
            try
            {
                var dialog = new ExternalNetworkDialog(externalNetworks);
                dialog.Owner = this;

                if (dialog.ShowDialog() == true)
                {
                    // تحديث قائمة الشبكات الخارجية
                    externalNetworks = dialog.ExternalIPs;

                    // إعادة تشغيل مدير المزامنة مع الشبكات الجديدة
                    RestartNetworkSync();

                    StatusText.Text = $"✅ تم تحديث إعدادات الشبكة - {externalNetworks.Count} شبكة خارجية";
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث الشبكات الخارجية: {externalNetworks.Count} شبكة");
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "❌ خطأ في إعدادات الشبكة الخارجية";
                MessageBox.Show($"❌ خطأ في إعدادات الشبكة الخارجية:\n\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"خطأ في ShowExternalNetworkDialog: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج النقر على زر حالة المزامنة
        /// </summary>
        private async void SyncStatusButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق القائمة المنسدلة
            SettingsPopup.IsOpen = false;

            try
            {
                if (universalSyncManager != null)
                {
                    // الحصول على حالة المزامنة
                    var syncStatus = await universalSyncManager.GetSyncStatusAsync();

                    // عرض حالة المزامنة في نافذة منبثقة
                    MessageBox.Show(syncStatus, "حالة المزامنة الشاملة", MessageBoxButton.OK, MessageBoxImage.Information);

                    StatusText.Text = "📊 تم عرض حالة المزامنة";
                }
                else
                {
                    MessageBox.Show("❌ مدير المزامنة الشامل غير متاح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في عرض حالة المزامنة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في عرض حالة المزامنة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج النقر على زر اختبار المزامنة
        /// </summary>
        private async void TestSyncButton_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق القائمة المنسدلة
            SettingsPopup.IsOpen = false;

            try
            {
                if (universalSyncManager != null)
                {
                    StatusText.Text = "🧪 جاري اختبار المزامنة...";

                    // تشغيل اختبار سريع
                    var testResult = await SyncTest.QuickSyncTest(universalSyncManager);

                    // عرض نتيجة الاختبار
                    MessageBox.Show(testResult, "نتيجة اختبار المزامنة", MessageBoxButton.OK,
                        testResult.StartsWith("✅") ? MessageBoxImage.Information : MessageBoxImage.Warning);

                    StatusText.Text = testResult.StartsWith("✅") ? "✅ اختبار المزامنة مكتمل" : "⚠️ اختبار المزامنة فشل";
                }
                else
                {
                    MessageBox.Show("❌ مدير المزامنة الشامل غير متاح", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    StatusText.Text = "❌ فشل اختبار المزامنة";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في اختبار المزامنة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في اختبار المزامنة: {ex.Message}");
                StatusText.Text = "❌ خطأ في اختبار المزامنة";
            }
        }

        /// <summary>
        /// إعادة تشغيل مدير المزامنة مع الإعدادات الجديدة
        /// </summary>
        private async void RestartNetworkSync()
        {
            try
            {
                // إيقاف المدير الحالي
                if (networkSyncManager != null)
                {
                    networkSyncManager.Stop();
                    networkSyncManager = null;
                }

                // انتظار قصير
                await Task.Delay(1000);

                // بدء مدير جديد مع الشبكات المحدثة
                InitializeNetworkSync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة تشغيل مدير المزامنة: {ex.Message}");
                StatusText.Text = "❌ خطأ في إعادة تشغيل المزامنة";
            }
        }

        /// <summary>
        /// إظهار معلومات IP والشبكة
        /// </summary>
        private void ShowIPInfo()
        {
            try
            {
                var dialog = new IPInfoDialog(this);
                dialog.Owner = this;
                dialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في الحصول على معلومات IP:\n\n{ex.Message}",
                               "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"خطأ في ShowIPInfo: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على مدير المزامنة (للاستخدام من النوافذ الأخرى)
        /// </summary>
        public NetworkSyncManager GetNetworkSyncManager()
        {
            return networkSyncManager;
        }

        /// <summary>
        /// الحصول على قائمة الشبكات الخارجية (للاستخدام من النوافذ الأخرى)
        /// </summary>
        public List<ExternalIPInfo> GetExternalNetworks()
        {
            return externalNetworks;
        }

        /// <summary>
        /// الحصول على معلومات IP مفصلة
        /// </summary>
        private string GetDetailedIPInfo()
        {
            var info = new StringBuilder();

            try
            {
                info.AppendLine("📍 معلومات IP والشبكة");
                info.AppendLine("=" + new string('=', 30));
                info.AppendLine();

                // معلومات الجهاز
                info.AppendLine("🖥️ معلومات الجهاز:");
                info.AppendLine($"• اسم الجهاز: {Environment.MachineName}");
                info.AppendLine($"• اسم المستخدم: {Environment.UserName}");
                info.AppendLine($"• نظام التشغيل: {Environment.OSVersion}");
                info.AppendLine();

                // عناوين IP المحلية
                info.AppendLine("🌐 عناوين IP المحلية:");
                var host = Dns.GetHostEntry(Dns.GetHostName());
                var localIPs = new List<string>();

                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork)
                    {
                        localIPs.Add(ip.ToString());
                        var ipType = GetIPType(ip.ToString());
                        info.AppendLine($"• {ip} ({ipType})");
                    }
                }

                if (localIPs.Count == 0)
                {
                    info.AppendLine("• لم يتم العثور على عناوين IP محلية");
                }
                info.AppendLine();

                // العنوان الأساسي المستخدم
                var primaryIP = GetLocalIPAddress();
                if (!string.IsNullOrEmpty(primaryIP))
                {
                    info.AppendLine("⭐ العنوان الأساسي المستخدم:");
                    info.AppendLine($"• {primaryIP}");
                    info.AppendLine();
                }

                // معلومات خادم المزامنة
                info.AppendLine("🔗 معلومات خادم المزامنة:");
                if (networkSyncManager != null)
                {
                    var connectedDevices = networkSyncManager.GetConnectedDevices();
                    info.AppendLine($"• حالة الخادم: ✅ يعمل");
                    info.AppendLine($"• المنفذ المستخدم: 8080 (أو بديل)");
                    info.AppendLine($"• الأجهزة المتصلة: {connectedDevices.Count}");

                    if (connectedDevices.Count > 0)
                    {
                        info.AppendLine("• قائمة الأجهزة المتصلة:");
                        foreach (var device in connectedDevices)
                        {
                            info.AppendLine($"  - {device}");
                        }
                    }
                }
                else
                {
                    info.AppendLine("• حالة الخادم: ❌ غير نشط");
                }
                info.AppendLine();

                // الشبكات الخارجية
                if (externalNetworks != null && externalNetworks.Count > 0)
                {
                    info.AppendLine("🌍 الشبكات الخارجية المضافة:");
                    info.AppendLine($"• العدد: {externalNetworks.Count}");
                    foreach (var network in externalNetworks)
                    {
                        var status = network.IsOnline ? "✅" : "❌";
                        info.AppendLine($"• {network.IP} - {network.Description} {status}");
                    }
                }
                else
                {
                    info.AppendLine("🌍 الشبكات الخارجية:");
                    info.AppendLine("• لا توجد شبكات خارجية مضافة");
                }
                info.AppendLine();

                // معلومات الاتصال
                info.AppendLine("📡 معلومات الاتصال:");
                try
                {
                    using (var client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromSeconds(5);
                        var externalIP = client.GetStringAsync("https://api.ipify.org").Result.Trim();
                        info.AppendLine($"• العنوان الخارجي: {externalIP}");
                    }
                }
                catch
                {
                    info.AppendLine("• العنوان الخارجي: غير متاح (لا يوجد اتصال إنترنت)");
                }

                // معلومات الشبكة المحلية
                var subnet = GetSubnetInfo(primaryIP);
                if (!string.IsNullOrEmpty(subnet))
                {
                    info.AppendLine($"• الشبكة المحلية: {subnet}");
                }

                info.AppendLine();
                info.AppendLine("💡 نصائح:");
                info.AppendLine("• استخدم العنوان الأساسي للمزامنة المحلية");
                info.AppendLine("• أضف العنوان الخارجي للأجهزة في شبكات أخرى");
                info.AppendLine("• تأكد من فتح المنفذ 8080 في جدار الحماية");
            }
            catch (Exception ex)
            {
                info.AppendLine($"❌ خطأ في جمع المعلومات: {ex.Message}");
            }

            return info.ToString();
        }

        /// <summary>
        /// تحديد نوع عنوان IP
        /// </summary>
        private string GetIPType(string ip)
        {
            if (ip.StartsWith("192.168.") || ip.StartsWith("10.") || ip.StartsWith("172."))
            {
                return "شبكة محلية";
            }
            else if (ip.StartsWith("127."))
            {
                return "محلي";
            }
            else if (ip.StartsWith("169.254."))
            {
                return "APIPA";
            }
            else
            {
                return "عام";
            }
        }

        /// <summary>
        /// الحصول على معلومات الشبكة الفرعية
        /// </summary>
        private string GetSubnetInfo(string ip)
        {
            try
            {
                if (!string.IsNullOrEmpty(ip))
                {
                    var parts = ip.Split('.');
                    if (parts.Length >= 3)
                    {
                        return $"{parts[0]}.{parts[1]}.{parts[2]}.x";
                    }
                }
            }
            catch { }

            return null;
        }







        /// <summary>
        /// الحصول على عنوان IP المحلي
        /// </summary>
        private string GetLocalIPAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        return ip.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على عنوان IP: {ex.Message}");
            }
            return null;
        }



        private void SaveApiKey(string apiKey)
        {
            try
            {
                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.GetDirectoryName(apiKeyFilePath);
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                // تشفير وحفظ المفتاح
                var encryptedKey = EncryptString(apiKey);
                File.WriteAllText(apiKeyFilePath, encryptedKey);

                System.Diagnostics.Debug.WriteLine("✅ تم حفظ API key بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ API key: {ex.Message}");
            }
        }

        private string EncryptString(string text)
        {
            try
            {
                var data = System.Text.Encoding.UTF8.GetBytes(text);
                var encrypted = System.Security.Cryptography.ProtectedData.Protect(data, null, System.Security.Cryptography.DataProtectionScope.CurrentUser);
                return Convert.ToBase64String(encrypted);
            }
            catch
            {
                return text; // في حالة فشل التشفير، احفظ النص كما هو
            }
        }



        #endregion

        #region OpenAI Integration

        private void LoadApiKey()
        {
            try
            {
                if (File.Exists(apiKeyFilePath))
                {
                    var encryptedKey = File.ReadAllText(apiKeyFilePath);
                    openAIApiKey = DecryptString(encryptedKey);
                    System.Diagnostics.Debug.WriteLine("✅ تم تحميل API key من الملف");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على ملف API key");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل API key: {ex.Message}");
            }
        }

        private string DecryptString(string encryptedText)
        {
            try
            {
                var data = Convert.FromBase64String(encryptedText);
                var decrypted = System.Security.Cryptography.ProtectedData.Unprotect(data, null, System.Security.Cryptography.DataProtectionScope.CurrentUser);
                return System.Text.Encoding.UTF8.GetString(decrypted);
            }
            catch
            {
                return encryptedText; // في حالة فشل فك التشفير، أرجع النص كما هو
            }
        }

        private void InitializeOpenAI()
        {
            try
            {
                if (!string.IsNullOrEmpty(openAIApiKey) && openAIApiKey != "YOUR_OPENAI_API_KEY_HERE")
                {
                    var openAIClientMain = new OpenAIClient(openAIApiKey);
                    openAIClient = openAIClientMain.GetChatClient("gpt-4o");
                    System.Diagnostics.Debug.WriteLine("✅ تم تهيئة OpenAI بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم تعيين مفتاح OpenAI API");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة OpenAI: {ex.Message}");
            }
        }

        private async Task<string> GenerateAIResponse(string query, List<ContentItem> relevantContent)
        {
            try
            {
                if (openAIClient == null)
                {
                    return "🤖 الذكاء الاصطناعي GPT-4o غير متاح حالياً. يرجى التحقق من إعدادات API.";
                }

                if (!isInternetConnected)
                {
                    return "🤖 الذكاء الاصطناعي GPT-4o يتطلب اتصال بالإنترنت.";
                }

                // إنشاء سياق من المحتوى الموجود
                var context = BuildContextFromContent(relevantContent);

                var messages = new List<ChatMessage>
                {
                    ChatMessage.CreateSystemMessage($@"أنت مساعد ذكي متقدم يعمل بنموذج GPT-4o لبرنامج Inzo IB لإدارة المحتوى.
مهمتك هي مساعدة المستخدم بناءً على المحتوى الموجود في البرنامج وتقديم تحليل عميق ومفيد.

السياق المتاح:
{context}

قواعد الإجابة المتقدمة:
1. أجب باللغة العربية الفصحى مع مراعاة الوضوح والبساطة
2. حلل المعلومات الموجودة في السياق بعمق واستخرج الأنماط والعلاقات
3. قدم إجابات شاملة ومفصلة مع أمثلة عملية
4. اقترح حلول إبداعية ومبتكرة للمشاكل
5. استخدم الرموز التعبيرية بذكاء لتحسين التواصل
6. إذا لم تجد معلومات كافية، اقترح خطة عمل مفصلة لإضافة المحتوى المطلوب
7. قدم نصائح متقدمة لتحسين إدارة المحتوى والإنتاجية
8. اربط المعلومات ببعضها البعض لتقديم رؤى أعمق"),

                    ChatMessage.CreateUserMessage($"السؤال: {query}")
                };

                var chatCompletionOptions = new ChatCompletionOptions
                {
                    Temperature = 0.8f,  // زيادة الإبداع قليلاً لاستغلال قوة GPT-4o
                    TopP = 0.95f         // تحسين جودة الإجابات
                };

                var response = await openAIClient.CompleteChatAsync(messages, chatCompletionOptions);
                return response.Value.Content[0].Text;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استدعاء OpenAI: {ex.Message}");
                return $"🤖 عذراً، حدث خطأ في الذكاء الاصطناعي GPT-4o: {ex.Message}";
            }
        }

        private string BuildContextFromContent(List<ContentItem> content)
        {
            if (content == null || content.Count == 0)
                return "لا يوجد محتوى متاح في البرنامج.";

            var contextBuilder = new System.Text.StringBuilder();
            contextBuilder.AppendLine("المحتوى المتاح في البرنامج:");

            foreach (var item in content.Take(10)) // أخذ أول 10 عناصر لتجنب تجاوز حد الرموز
            {
                contextBuilder.AppendLine($"\n📁 القسم: {item.Section}");
                if (!string.IsNullOrEmpty(item.Title))
                    contextBuilder.AppendLine($"📝 العنوان: {item.Title}");
                if (!string.IsNullOrEmpty(item.Content))
                    contextBuilder.AppendLine($"📄 المحتوى: {item.Content.Substring(0, Math.Min(200, item.Content.Length))}...");
                if (item.AttachedFiles.Any())
                    contextBuilder.AppendLine($"📎 ملفات مرفقة: {item.AttachedFiles.Count} ملف");
                contextBuilder.AppendLine("---");
            }

            return contextBuilder.ToString();
        }

        #endregion

        #region Firestore Integration

        /// <summary>
        /// تهيئة مدير Firestore
        /// </summary>
        private void InitializeFirestore()
        {
            try
            {
                firestoreManager = new FirestoreManager(firestoreSettingsFilePath);

                // ربط الأحداث
                firestoreManager.ConnectionStatusChanged += OnFirestoreConnectionStatusChanged;
                firestoreManager.DataUpdated += OnFirestoreDataUpdated;

                // محاولة تهيئة الاتصال إذا كانت الإعدادات موجودة
                _ = Task.Run(async () =>
                {
                    var initialized = await firestoreManager.InitializeAsync();
                    if (initialized)
                    {
                        useFirestore = true;
                        System.Diagnostics.Debug.WriteLine("✅ تم تفعيل Firestore تلقائياً");

                        // تحميل البيانات من Firestore
                        await LoadDataFromFirestoreAsync();
                    }
                });

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة مدير Firestore");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة مدير Firestore: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج حدث تغيير حالة اتصال Firestore
        /// </summary>
        private void OnFirestoreConnectionStatusChanged(bool isConnected, string message)
        {
            Dispatcher.Invoke(() =>
            {
                if (StatusText != null)
                {
                    StatusText.Text = $"🔥 Firestore: {message}";
                }
                System.Diagnostics.Debug.WriteLine($"🔥 Firestore Status: {message}");
            });
        }

        /// <summary>
        /// معالج حدث تحديث بيانات Firestore
        /// </summary>
        private void OnFirestoreDataUpdated(List<ContentItem> items)
        {
            Dispatcher.Invoke(() =>
            {
                try
                {
                    contentItems = items;
                    LoadCurrentSectionContent();
                    System.Diagnostics.Debug.WriteLine($"🔥 تم تحديث البيانات من Firestore: {items.Count} عنصر");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث البيانات من Firestore: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// عرض نافذة إعدادات Firestore
        /// </summary>
        private void ShowFirestoreSettings()
        {
            try
            {
                var settingsDialog = new FirestoreSettingsDialog(firestoreManager);
                settingsDialog.Owner = this;

                if (settingsDialog.ShowDialog() == true)
                {
                    if (settingsDialog.IsSettingsSaved)
                    {
                        useFirestore = true;
                        StatusText.Text = "✅ تم تكوين Firestore بنجاح! سيتم استخدامه لحفظ البيانات";

                        // تحميل البيانات من Firestore
                        _ = Task.Run(async () => await LoadDataFromFirestoreAsync());
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض إعدادات Firestore:\n{ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات من Firestore بشكل غير متزامن مع المزامنة الذكية
        /// </summary>
        private async Task LoadDataFromFirestoreAsync()
        {
            try
            {
                if (firestoreManager != null)
                {
                    System.Diagnostics.Debug.WriteLine("🔄 بدء المزامنة مع Firestore...");

                    // الحصول على البيانات المحلية الحالية
                    List<ContentItem> localItems = null;
                    await Dispatcher.InvokeAsync(() =>
                    {
                        localItems = contentItems?.ToList() ?? new List<ContentItem>();
                        System.Diagnostics.Debug.WriteLine($"📁 البيانات المحلية الحالية: {localItems.Count} عنصر");
                    });

                    // استخدام المزامنة الذكية
                    var syncedItems = await firestoreManager.SyncDataAsync(localItems);

                    if (syncedItems != null && syncedItems.Count > 0)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            contentItems = syncedItems;
                            LoadCurrentSectionContent();
                            System.Diagnostics.Debug.WriteLine($"✅ تم تحديث البيانات بالمزامنة الذكية: {syncedItems.Count} عنصر");
                        });

                        // إعداد المزامنة التلقائية
                        await SetupRealtimeSyncAsync();
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ لم يتم الحصول على بيانات من المزامنة - الاحتفاظ بالبيانات المحلية");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في المزامنة مع Firestore: {ex.Message}");
                System.Diagnostics.Debug.WriteLine("📁 الاحتفاظ بالبيانات المحلية الحالية");

                // لا نحتاج لإعادة تحميل البيانات المحلية لأنها محملة بالفعل
                Dispatcher.Invoke(() =>
                {
                    LoadCurrentSectionContent(); // فقط تحديث العرض
                });
            }
        }

        /// <summary>
        /// إعداد المزامنة التلقائية مع Firestore
        /// </summary>
        private async Task SetupRealtimeSyncAsync()
        {
            try
            {
                if (firestoreManager != null && useFirestore)
                {
                    var success = await firestoreManager.SetupRealtimeSyncAsync();
                    if (success)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ تم إعداد المزامنة التلقائية مع Firestore");

                        Dispatcher.Invoke(() =>
                        {
                            if (StatusText != null)
                            {
                                StatusText.Text = "✅ تم تفعيل المزامنة التلقائية مع Firestore";
                            }
                        });
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("❌ فشل في إعداد المزامنة التلقائية");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد المزامنة التلقائية: {ex.Message}");
            }
        }

        #endregion

        #region Telegram Bot Token Management

        private void LoadBotToken()
        {
            try
            {
                if (File.Exists(botTokenFilePath))
                {
                    var encryptedToken = File.ReadAllText(botTokenFilePath);
                    telegramBotToken = DecryptString(encryptedToken);
                    System.Diagnostics.Debug.WriteLine("✅ تم تحميل توكن البوت من الملف");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على ملف توكن البوت");
                    telegramBotToken = ""; // توكن فارغ
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل توكن البوت: {ex.Message}");
                telegramBotToken = ""; // توكن فارغ في حالة الخطأ
            }
        }

        private void SaveBotToken(string token)
        {
            try
            {
                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.GetDirectoryName(botTokenFilePath);
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                // تشفير وحفظ التوكن
                var encryptedToken = EncryptString(token);
                File.WriteAllText(botTokenFilePath, encryptedToken);

                System.Diagnostics.Debug.WriteLine("✅ تم حفظ توكن البوت بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ توكن البوت: {ex.Message}");
            }
        }

        private bool IsBotTokenValid()
        {
            return !string.IsNullOrEmpty(telegramBotToken) &&
                   telegramBotToken.Contains(":") &&
                   telegramBotToken.Length > 20;
        }

        private void ShowBotTokenDialog()
        {
            try
            {
                var tokenDialog = new BotTokenDialog(telegramBotToken);
                tokenDialog.Owner = this;

                if (tokenDialog.ShowDialog() == true)
                {
                    var newToken = tokenDialog.BotToken;

                    if (!string.IsNullOrEmpty(newToken))
                    {
                        telegramBotToken = newToken;

                        // حفظ التوكن
                        SaveBotToken(newToken);

                        // إعادة تهيئة البوت
                        InitializeTelegramBot();

                        StatusText.Text = "✅ تم حفظ توكن البوت! جاري الاتصال...";

                        MessageBox.Show("✅ تم حفظ توكن بوت التيليجرام بنجاح!\n\n🤖 سيتم الآن محاولة الاتصال بالبوت.\n\n📱 تأكد من أن البوت نشط ويمكن الوصول إليه.",
                            "تم الحفظ بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    StatusText.Text = "ℹ️ تم إلغاء إعدادات البوت";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في عرض نافذة إعدادات البوت:\n\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Pending Messages Management

        private void LoadPendingMessages()
        {
            try
            {
                if (File.Exists(pendingMessagesFilePath))
                {
                    var json = File.ReadAllText(pendingMessagesFilePath);
                    var loadedMessages = JsonSerializer.Deserialize<List<PendingMessage>>(json) ?? new List<PendingMessage>();

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {loadedMessages.Count} رسالة معلقة");

                    // إضافة الرسائل المعلقة إلى البرنامج
                    foreach (var message in loadedMessages)
                    {
                        try
                        {
                            Dispatcher.Invoke(() =>
                            {
                                AddTelegramMessageToInzoIB(message.Sender, message.Content, message.Timestamp);
                            });

                            System.Diagnostics.Debug.WriteLine($"📨 تم إضافة رسالة معلقة من {message.Sender}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة رسالة معلقة: {ex.Message}");
                        }
                    }

                    // حذف ملف الرسائل المعلقة بعد تحميلها
                    File.Delete(pendingMessagesFilePath);
                    System.Diagnostics.Debug.WriteLine("🗑️ تم حذف ملف الرسائل المعلقة بعد التحميل");

                    if (loadedMessages.Count > 0)
                    {
                        Dispatcher.Invoke(() =>
                        {
                            StatusText.Text = $"📬 تم تحميل {loadedMessages.Count} رسالة وصلت أثناء إغلاق البرنامج";
                        });
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ لا توجد رسائل معلقة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الرسائل المعلقة: {ex.Message}");
            }
        }

        private void SavePendingMessage(string sender, string content, DateTime timestamp, long chatId, string chatTitle, string chatType, bool isFromGroupOrChannel)
        {
            try
            {
                var pendingMessage = new PendingMessage
                {
                    Sender = sender,
                    Content = content,
                    Timestamp = timestamp,
                    ChatId = chatId,
                    ChatTitle = chatTitle,
                    ChatType = chatType,
                    IsFromGroupOrChannel = isFromGroupOrChannel
                };

                // تحميل الرسائل المعلقة الموجودة
                var existingMessages = new List<PendingMessage>();
                if (File.Exists(pendingMessagesFilePath))
                {
                    var json = File.ReadAllText(pendingMessagesFilePath);
                    existingMessages = JsonSerializer.Deserialize<List<PendingMessage>>(json) ?? new List<PendingMessage>();
                }

                // إضافة الرسالة الجديدة
                existingMessages.Add(pendingMessage);

                // حفظ جميع الرسائل
                var options = new JsonSerializerOptions { WriteIndented = true };
                var newJson = JsonSerializer.Serialize(existingMessages, options);

                // إنشاء مجلد Data إذا لم يكن موجوداً
                var dataFolder = Path.GetDirectoryName(pendingMessagesFilePath);
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                File.WriteAllText(pendingMessagesFilePath, newJson);
                System.Diagnostics.Debug.WriteLine($"💾 تم حفظ رسالة معلقة من {sender}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الرسالة المعلقة: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف رسالة من البرنامج بناءً على معرف تيليجرام
        /// </summary>
        private bool DeleteTelegramMessageFromProgram(long chatId, int messageId)
        {
            try
            {
                var messageKey = $"{chatId}_{messageId}";

                if (telegramMessageMap.TryGetValue(messageKey, out ContentItem messageItem))
                {
                    // حذف الرسالة من قائمة المحتوى
                    contentItems.Remove(messageItem);

                    // حذف الرسالة من قاموس التتبع
                    telegramMessageMap.Remove(messageKey);

                    // حفظ البيانات
                    SaveData();

                    // تحديث العرض إذا كان المستخدم في قسم INZO IB
                    if (currentSection == "INZO IB")
                    {
                        Dispatcher.Invoke(() =>
                        {
                            LoadCurrentSectionContent();
                        });
                    }

                    System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف الرسالة من البرنامج: {messageKey}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على الرسالة للحذف: {messageKey}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف الرسالة من البرنامج: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// معالجة أوامر حذف الرسائل
        /// </summary>
        private async Task<bool> HandleDeleteCommand(ITelegramBotClient botClient, TelegramTypes.Message message, CancellationToken cancellationToken)
        {
            try
            {
                var messageText = message.Text?.Trim();

                // التحقق من أن الرسالة تبدأ بأمر الحذف
                if (messageText?.StartsWith("/delete_msg") == true)
                {
                    var chatId = message.Chat.Id;
                    var parts = messageText.Split(' ');

                    if (parts.Length >= 2 && int.TryParse(parts[1], out int targetMessageId))
                    {
                        // حذف الرسالة من البرنامج
                        bool deleted = DeleteTelegramMessageFromProgram(chatId, targetMessageId);

                        if (deleted)
                        {
                            await botClient.SendTextMessageAsync(
                                chatId: chatId,
                                text: $"✅ تم حذف الرسالة رقم {targetMessageId} من البرنامج",
                                cancellationToken: cancellationToken);
                        }
                        else
                        {
                            await botClient.SendTextMessageAsync(
                                chatId: chatId,
                                text: $"❌ لم يتم العثور على الرسالة رقم {targetMessageId}",
                                cancellationToken: cancellationToken);
                        }

                        return true; // تم معالجة الأمر
                    }
                    else
                    {
                        await botClient.SendTextMessageAsync(
                            chatId: chatId,
                            text: "❌ استخدام خاطئ. الاستخدام الصحيح: /delete_msg [رقم_الرسالة]",
                            cancellationToken: cancellationToken);

                        return true; // تم معالجة الأمر
                    }
                }

                // التحقق من أمر عرض معرفات الرسائل
                if (messageText == "/list_messages")
                {
                    var chatId = message.Chat.Id;
                    var chatMessages = telegramMessageMap
                        .Where(kvp => kvp.Key.StartsWith($"{chatId}_"))
                        .Take(10) // أحدث 10 رسائل
                        .ToList();

                    if (chatMessages.Any())
                    {
                        var messageList = "📋 آخر الرسائل في هذه المحادثة:\n\n";
                        foreach (var msg in chatMessages)
                        {
                            var parts = msg.Key.Split('_');
                            if (parts.Length == 2)
                            {
                                var content = msg.Value.Content.Length > 50
                                    ? msg.Value.Content.Substring(0, 50) + "..."
                                    : msg.Value.Content;
                                messageList += $"🔹 ID: {parts[1]} - {content}\n";
                            }
                        }
                        messageList += "\n💡 أوامر مفيدة:";
                        messageList += "\n🗑️ /delete_msg [رقم] - حذف رسالة";
                        messageList += "\n🔍 /check_deleted - فحص الرسائل المحذوفة";

                        await botClient.SendTextMessageAsync(
                            chatId: chatId,
                            text: messageList,
                            cancellationToken: cancellationToken);
                    }
                    else
                    {
                        await botClient.SendTextMessageAsync(
                            chatId: chatId,
                            text: "📭 لا توجد رسائل محفوظة في هذه المحادثة",
                            cancellationToken: cancellationToken);
                    }

                    return true; // تم معالجة الأمر
                }

                // التحقق من أمر فحص الرسائل المحذوفة
                if (messageText == "/check_deleted")
                {
                    var chatId = message.Chat.Id;

                    await botClient.SendTextMessageAsync(
                        chatId: chatId,
                        text: "🔍 جاري فحص الرسائل المحذوفة...",
                        cancellationToken: cancellationToken);

                    // فحص الرسائل المحذوفة في هذه المحادثة
                    var chatMessages = telegramMessageMap
                        .Where(kvp => kvp.Key.StartsWith($"{chatId}_"))
                        .ToList();

                    int deletedCount = await CheckDeletedMessagesInChat(chatId, chatMessages);

                    if (deletedCount > 0)
                    {
                        await botClient.SendTextMessageAsync(
                            chatId: chatId,
                            text: $"✅ تم العثور على {deletedCount} رسالة محذوفة وإزالتها من البرنامج",
                            cancellationToken: cancellationToken);
                    }
                    else
                    {
                        await botClient.SendTextMessageAsync(
                            chatId: chatId,
                            text: "✅ لم يتم العثور على رسائل محذوفة",
                            cancellationToken: cancellationToken);
                    }

                    return true; // تم معالجة الأمر
                }

                return false; // لم يتم معالجة أي أمر
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة أمر الحذف: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعادة بناء قاموس تتبع الرسائل من البيانات المحفوظة
        /// </summary>
        private void RebuildTelegramMessageMap()
        {
            try
            {
                telegramMessageMap.Clear();

                if (contentItems != null)
                {
                    foreach (var item in contentItems)
                    {
                        if (item.IsTelegramMessage && item.TelegramMessageId > 0 && item.TelegramChatId != 0)
                        {
                            var messageKey = $"{item.TelegramChatId}_{item.TelegramMessageId}";
                            telegramMessageMap[messageKey] = item;
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"🔗 تم إعادة بناء قاموس تتبع الرسائل: {telegramMessageMap.Count} رسالة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة بناء قاموس تتبع الرسائل: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة مؤقت فحص الرسائل المحذوفة (معطل افتراضياً)
        /// </summary>
        private void InitializeDeletedMessageChecker()
        {
            try
            {
                // المؤقت معطل افتراضياً لتجنب الحمل الزائد على API
                // يمكن تفعيله يدوياً عند الحاجة
                deletedMessageCheckTimer = new DispatcherTimer();
                deletedMessageCheckTimer.Interval = TimeSpan.FromMinutes(10); // فحص كل 10 دقائق
                deletedMessageCheckTimer.Tick += async (sender, e) => await CheckAllDeletedMessages();
                // deletedMessageCheckTimer.Start(); // معطل افتراضياً

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة مؤقت فحص الرسائل المحذوفة (معطل افتراضياً)");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة مؤقت فحص الرسائل المحذوفة: {ex.Message}");
            }
        }

        /// <summary>
        /// فحص الرسائل المحذوفة في قناة محددة
        /// </summary>
        private async Task CheckAllDeletedMessages()
        {
            if (telegramBot == null || telegramMessageMap.Count == 0)
                return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 بدء فحص {telegramMessageMap.Count} رسالة للتحقق من الحذف");

                // تجميع الرسائل حسب المحادثة
                var chatGroups = telegramMessageMap
                    .GroupBy(kvp => kvp.Key.Split('_')[0])
                    .Where(g => long.TryParse(g.Key, out _))
                    .ToList();

                int totalDeletedCount = 0;

                foreach (var chatGroup in chatGroups)
                {
                    if (long.TryParse(chatGroup.Key, out long chatId))
                    {
                        int deletedInChat = await CheckDeletedMessagesInChat(chatId, chatGroup.ToList());
                        totalDeletedCount += deletedInChat;
                    }
                }

                if (totalDeletedCount > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف {totalDeletedCount} رسالة محذوفة من البرنامج");

                    Dispatcher.Invoke(() =>
                    {
                        if (currentSection == "INZO IB")
                        {
                            LoadCurrentSectionContent();
                        }
                        StatusText.Text = $"🗑️ تم حذف {totalDeletedCount} رسالة محذوفة من تيليجرام";
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص الرسائل المحذوفة: {ex.Message}");
            }
        }

        /// <summary>
        /// فحص الرسائل المحذوفة في محادثة محددة بطريقة آمنة
        /// </summary>
        private async Task<int> CheckDeletedMessagesInChat(long chatId, List<KeyValuePair<string, ContentItem>> chatMessages)
        {
            int deletedCount = 0;

            try
            {
                // فحص عينة من الرسائل (أحدث 10 رسائل) لتجنب الحمل الزائد
                var samplesToCheck = chatMessages.Take(10).ToList();

                System.Diagnostics.Debug.WriteLine($"🔍 فحص {samplesToCheck.Count} رسالة في المحادثة {chatId}");

                foreach (var messageEntry in samplesToCheck)
                {
                    var parts = messageEntry.Key.Split('_');
                    if (parts.Length == 2 && int.TryParse(parts[1], out int messageId))
                    {
                        try
                        {
                            // محاولة إعادة توجيه الرسالة إلى نفس المحادثة (بدون إشعار)
                            // هذه طريقة آمنة للتحقق من وجود الرسالة
                            var forwardedMessage = await telegramBot.ForwardMessageAsync(
                                chatId: chatId,
                                fromChatId: chatId,
                                messageId: messageId,
                                disableNotification: true
                            );

                            // إذا نجحت العملية، الرسالة موجودة
                            // نحذف الرسالة المعاد توجيهها فوراً لتجنب التكرار
                            try
                            {
                                await telegramBot.DeleteMessageAsync(chatId, forwardedMessage.MessageId);
                            }
                            catch
                            {
                                // تجاهل خطأ حذف الرسالة المعاد توجيهها
                            }

                            System.Diagnostics.Debug.WriteLine($"✅ الرسالة {messageId} موجودة");
                        }
                        catch (Exception ex)
                        {
                            // إذا فشلت العملية، الرسالة محذوفة أو غير متاحة
                            if (ex.Message.Contains("message not found") ||
                                ex.Message.Contains("MESSAGE_ID_INVALID") ||
                                ex.Message.Contains("message to forward not found"))
                            {
                                DeleteTelegramMessageFromProgram(chatId, messageId);
                                deletedCount++;
                                System.Diagnostics.Debug.WriteLine($"🗑️ تم اكتشاف وحذف رسالة محذوفة: {messageEntry.Key}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في فحص الرسالة {messageId}: {ex.Message}");
                            }
                        }

                        // تأخير قصير لتجنب الحد الأقصى للطلبات
                        await Task.Delay(500);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص رسائل المحادثة {chatId}: {ex.Message}");
            }

            return deletedCount;
        }

        #endregion

        #region Telegram Bot Integration

        private async void InitializeTelegramBot()
        {
            try
            {
                // التحقق من وجود توكن صالح
                if (!IsBotTokenValid())
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ توكن البوت غير موجود أو غير صالح");

                    Dispatcher.Invoke(() =>
                    {
                        StatusText.Text = "⚠️ يرجى إدخال توكن بوت التيليجرام";
                        ShowBotTokenDialog();
                    });
                    return;
                }

                telegramBot = new TelegramBotClient(telegramBotToken);
                telegramCancellationToken = new CancellationTokenSource();

                // اختبار الاتصال بالبوت أولاً
                var me = await telegramBot.GetMeAsync();
                System.Diagnostics.Debug.WriteLine($"🤖 تم الاتصال بالبوت: {me.FirstName} (@{me.Username})");

                // إعداد خيارات الاستقبال لتشمل القنوات والمجموعات
                var receiverOptions = new ReceiverOptions
                {
                    AllowedUpdates = new[]
                    {
                        UpdateType.Message,          // رسائل عادية
                        UpdateType.CallbackQuery,    // الضغط على الأزرار
                        UpdateType.ChannelPost,      // رسائل القنوات
                        UpdateType.EditedMessage,    // الرسائل المعدلة
                        UpdateType.EditedChannelPost // رسائل القنوات المعدلة
                    },
                    ThrowPendingUpdates = false // الاحتفاظ بالرسائل المعلقة
                };

                // بدء استقبال الرسائل
                telegramBot.StartReceiving(
                    updateHandler: HandleUpdateAsync,
                    pollingErrorHandler: HandlePollingErrorAsync,
                    receiverOptions: receiverOptions,
                    cancellationToken: telegramCancellationToken.Token
                );

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة بوت التيليجرام بنجاح وبدء استقبال الرسائل");

                // تحديث شريط الحالة في الـ UI thread
                Dispatcher.Invoke(() =>
                {
                    StatusText.Text = $"🤖 تم ربط بوت التيليجرام ({me.FirstName}) بقسم Inzo IB";
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة بوت التيليجرام: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                Dispatcher.Invoke(() =>
                {
                    string errorMessage = "❌ خطأ في ربط بوت التيليجرام";

                    if (ex.Message.Contains("Unauthorized"))
                    {
                        errorMessage = "❌ توكن البوت غير صحيح - انقر 'إعدادات البوت'";
                    }
                    else if (ex.Message.Contains("Not Found"))
                    {
                        errorMessage = "❌ البوت غير موجود - تحقق من التوكن";
                    }
                    else if (ex.Message.Contains("network") || ex.Message.Contains("timeout"))
                    {
                        errorMessage = "❌ مشكلة في الاتصال بالإنترنت";
                    }

                    StatusText.Text = errorMessage;

                    // عرض رسالة مفصلة للمستخدم
                    MessageBox.Show($"❌ فشل في الاتصال ببوت التيليجرام\n\n" +
                                  $"السبب: {ex.Message}\n\n" +
                                  $"💡 الحلول المقترحة:\n" +
                                  $"• تحقق من توكن البوت (زر 'إعدادات البوت')\n" +
                                  $"• تأكد من اتصال الإنترنت\n" +
                                  $"• تأكد من أن البوت نشط\n" +
                                  $"• جرب إعادة تشغيل البرنامج",
                                  "خطأ في بوت التيليجرام", MessageBoxButton.OK, MessageBoxImage.Warning);
                });
            }
        }

        /// <summary>
        /// تحميل إعدادات الشبكات الخارجية
        /// </summary>
        private void LoadExternalNetworks()
        {
            try
            {
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "external_networks.json");
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    var loadedNetworks = System.Text.Json.JsonSerializer.Deserialize<List<ExternalIPInfo>>(json);

                    if (loadedNetworks != null)
                    {
                        externalNetworks = loadedNetworks;
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {externalNetworks.Count} شبكة خارجية");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الشبكات الخارجية: {ex.Message}");
            }
        }

        private async void InitializeNetworkSync()
        {
            try
            {
                networkSyncManager = new NetworkSyncManager(this, externalNetworks);

                // ربط الأحداث
                networkSyncManager.MessageReceived += OnNetworkMessageReceived;
                networkSyncManager.DeviceConnected += OnDeviceConnected;
                networkSyncManager.DeviceDisconnected += OnDeviceDisconnected;

                // ربط أحداث المزامنة الجديدة
                networkSyncManager.DataSyncReceived += OnDataSyncReceived;
                networkSyncManager.SettingsUpdateReceived += OnSettingsUpdateReceived;

                // بدء الخادم
                await networkSyncManager.StartServerAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة مدير المزامنة عبر الشبكة بنجاح");

                Dispatcher.Invoke(() =>
                {
                    StatusText.Text = "🌐 تم تفعيل المزامنة عبر الشبكة - البحث عن الأجهزة...";
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة مدير المزامنة: {ex.Message}");

                Dispatcher.Invoke(() =>
                {
                    StatusText.Text = $"❌ خطأ في تفعيل المزامنة عبر الشبكة: {ex.Message}";
                });
            }
        }

        private void InitializeUniversalSync()
        {
            try
            {
                // إنشاء مدير المزامنة الشامل
                universalSyncManager = new UniversalSyncManager(this, networkSyncManager, firestoreManager, telegramBot);

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة مدير المزامنة الشامل بنجاح");

                Dispatcher.Invoke(() =>
                {
                    StatusText.Text = "🌍 تم تفعيل المزامنة الشاملة - جاهز للعمل";
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة مدير المزامنة الشامل: {ex.Message}");

                Dispatcher.Invoke(() =>
                {
                    StatusText.Text = $"❌ خطأ في تفعيل المزامنة الشاملة: {ex.Message}";
                });
            }
        }

        private void OnNetworkMessageReceived(string sender, string content, DateTime timestamp)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // إضافة الرسالة إلى البرنامج مع تمييزها كرسالة مزامنة
                    AddTelegramMessageToInzoIB($"{sender} (مزامنة)", content, timestamp, true);

                    // تحديث شريط الحالة
                    StatusText.Text = $"📱 رسالة مزامنة من {sender} - {DateTime.Now:HH:mm:ss}";
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الرسالة المزامنة: {ex.Message}");
            }
        }

        private void OnDeviceConnected(string deviceIP)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    var connectedDevices = networkSyncManager.GetConnectedDevices();
                    StatusText.Text = $"🔗 متصل مع {connectedDevices.Count} جهاز - آخر اتصال: {deviceIP}";
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة اتصال الجهاز: {ex.Message}");
            }
        }

        private void OnDeviceDisconnected(string deviceIP)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    var connectedDevices = networkSyncManager.GetConnectedDevices();
                    StatusText.Text = $"❌ انقطع الاتصال مع {deviceIP} - متصل مع {connectedDevices.Count} جهاز";
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة انقطاع الاتصال: {ex.Message}");
            }
        }

        // معالج استقبال مزامنة البيانات
        private void OnDataSyncReceived(List<ContentItem> receivedItems)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    System.Diagnostics.Debug.WriteLine($"📥 تم استقبال مزامنة البيانات: {receivedItems.Count} عنصر");

                    // دمج البيانات المستقبلة مع البيانات الحالية
                    MergeReceivedData(receivedItems);

                    // تحديث العرض
                    LoadCurrentSectionContent();

                    // تحديث شريط الحالة
                    StatusText.Text = $"🔄 تم تحديث البيانات: {receivedItems.Count} عنصر - {DateTime.Now:HH:mm:ss}";

                    // عرض إشعار Toast
                    ShowSyncNotification($"تم استقبال {receivedItems.Count} عنصر جديد", "🔄 مزامنة البيانات");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة مزامنة البيانات: {ex.Message}");
            }
        }

        // معالج استقبال تحديث الإعدادات
        private void OnSettingsUpdateReceived(string settingKey, object settingValue)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    System.Diagnostics.Debug.WriteLine($"⚙️ تم استقبال تحديث الإعداد: {settingKey} = {settingValue}");

                    // تطبيق الإعداد المحدث
                    ApplyReceivedSetting(settingKey, settingValue);

                    // تحديث شريط الحالة
                    StatusText.Text = $"⚙️ تم تحديث الإعداد: {settingKey} - {DateTime.Now:HH:mm:ss}";

                    // عرض إشعار Toast
                    ShowSyncNotification($"تم تحديث الإعداد: {settingKey}", "⚙️ مزامنة الإعدادات");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة تحديث الإعدادات: {ex.Message}");
            }
        }

        // دمج البيانات المستقبلة
        private void MergeReceivedData(List<ContentItem> receivedItems)
        {
            try
            {
                foreach (var receivedItem in receivedItems)
                {
                    // البحث عن العنصر الموجود بنفس المعرف
                    var existingItem = contentItems.FirstOrDefault(x => x.Id == receivedItem.Id);

                    if (existingItem != null)
                    {
                        // تحديث العنصر الموجود إذا كان المستقبل أحدث
                        if (receivedItem.Timestamp > existingItem.Timestamp)
                        {
                            var index = contentItems.IndexOf(existingItem);
                            contentItems[index] = receivedItem;
                            System.Diagnostics.Debug.WriteLine($"🔄 تم تحديث العنصر: {receivedItem.Content?.Substring(0, Math.Min(50, receivedItem.Content.Length ?? 0))}...");
                        }
                    }
                    else
                    {
                        // إضافة عنصر جديد
                        contentItems.Add(receivedItem);
                        System.Diagnostics.Debug.WriteLine($"➕ تم إضافة عنصر جديد: {receivedItem.Content?.Substring(0, Math.Min(50, receivedItem.Content.Length ?? 0))}...");
                    }
                }

                // حفظ البيانات المحدثة
                SaveData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في دمج البيانات: {ex.Message}");
            }
        }

        // تطبيق الإعداد المستقبل
        private void ApplyReceivedSetting(string settingKey, object settingValue)
        {
            try
            {
                // يمكن إضافة منطق تطبيق الإعدادات المختلفة هنا
                switch (settingKey.ToLower())
                {
                    case "theme":
                        // تطبيق السمة
                        System.Diagnostics.Debug.WriteLine($"تطبيق السمة: {settingValue}");
                        break;

                    case "autosync":
                        // تطبيق إعداد المزامنة التلقائية
                        if (bool.TryParse(settingValue?.ToString(), out bool autoSync))
                        {
                            universalSyncManager?.SetSyncEnabled(autoSync);
                            System.Diagnostics.Debug.WriteLine($"تطبيق المزامنة التلقائية: {autoSync}");
                        }
                        break;

                    default:
                        System.Diagnostics.Debug.WriteLine($"إعداد غير معروف: {settingKey}");
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق الإعداد: {ex.Message}");
            }
        }

        // دالة مساعدة لمزامنة الإعدادات عند تغييرها
        private async void SyncSettingChange(string settingKey, object settingValue)
        {
            try
            {
                if (universalSyncManager != null)
                {
                    await universalSyncManager.SyncSettingsUniversallyAsync(settingKey, settingValue);
                    System.Diagnostics.Debug.WriteLine($"✅ تم مزامنة الإعداد: {settingKey} = {settingValue}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة الإعداد: {ex.Message}");
            }
        }

        // عرض إشعار المزامنة
        private void ShowSyncNotification(string message, string title = "إشعار المزامنة")
        {
            try
            {
                // عرض Toast notification
                ToastWindow.Show($"{title}: {message}");

                System.Diagnostics.Debug.WriteLine($"📢 إشعار المزامنة: {message}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في عرض إشعار المزامنة: {ex.Message}");
            }
        }

        private async Task HandleUpdateAsync(ITelegramBotClient botClient, TelegramTypes.Update update, CancellationToken cancellationToken)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 تم استلام تحديث من التيليجرام: {update.Type}");

                // معالجة الرسائل النصية العادية
                if (update.Message is { } message)
                {
                    await HandleTextMessage(botClient, message, cancellationToken);
                    return;
                }

                // معالجة رسائل القنوات
                if (update.ChannelPost is { } channelPost)
                {
                    await HandleTextMessage(botClient, channelPost, cancellationToken);
                    return;
                }

                // معالجة الرسائل المعدلة
                if (update.EditedMessage is { } editedMessage)
                {
                    await HandleTextMessage(botClient, editedMessage, cancellationToken);
                    return;
                }

                // معالجة رسائل القنوات المعدلة
                if (update.EditedChannelPost is { } editedChannelPost)
                {
                    await HandleTextMessage(botClient, editedChannelPost, cancellationToken);
                    return;
                }

                // معالجة الضغط على الأزرار (CallbackQuery)
                if (update.CallbackQuery is { } callbackQuery)
                {
                    await HandleCallbackQuery(botClient, callbackQuery, cancellationToken);
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"⚠️ نوع التحديث غير مدعوم: {update.Type}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في معالجة تحديث التيليجرام: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        private async Task HandleTextMessage(ITelegramBotClient botClient, TelegramTypes.Message message, CancellationToken cancellationToken)
        {
            try
            {
                if (message.Text is not { } messageText)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ الرسالة ليست نصية");
                    return;
                }

                // التحقق من أوامر الحذف أولاً
                bool isCommand = await HandleDeleteCommand(botClient, message, cancellationToken);
                if (isCommand)
                {
                    return; // تم معالجة الأمر، لا نحتاج لمعالجة إضافية
                }

                var chatId = message.Chat.Id;
                var chatType = message.Chat.Type;
                var chatTitle = message.Chat.Title ?? "محادثة خاصة";
                var userName = message.From?.Username ?? message.From?.FirstName ?? "مجهول";
                var messageDate = message.Date.ToLocalTime(); // تحويل من UTC إلى التوقيت المحلي

                // تحديد مصدر الرسالة
                string messageSource = "";
                bool isFromGroupOrChannel = false;

                switch (chatType)
                {
                    case TelegramTypes.Enums.ChatType.Private:
                        messageSource = $"{userName} (محادثة خاصة)";
                        break;
                    case TelegramTypes.Enums.ChatType.Group:
                        messageSource = $"{userName} من مجموعة: {chatTitle}";
                        isFromGroupOrChannel = true;
                        break;
                    case TelegramTypes.Enums.ChatType.Supergroup:
                        messageSource = $"{userName} من مجموعة: {chatTitle}";
                        isFromGroupOrChannel = true;
                        break;
                    case TelegramTypes.Enums.ChatType.Channel:
                        messageSource = $"قناة: {chatTitle}";
                        isFromGroupOrChannel = true;
                        break;
                    default:
                        messageSource = $"{userName} من {chatType}";
                        break;
                }

                System.Diagnostics.Debug.WriteLine($"📨 رسالة جديدة من {messageSource} (ID: {chatId}): {messageText}");

                // إضافة الرسالة إلى قسم Inzo IB وحفظ معرف فريد
                string messageId = null;
                Dispatcher.Invoke(() =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"🔄 بدء إضافة الرسالة إلى البرنامج من {messageSource}");
                        messageId = AddTelegramMessageToInzoIB(messageSource, messageText, messageDate, false, message.MessageId, chatId);
                        System.Diagnostics.Debug.WriteLine("✅ تم إضافة الرسالة إلى البرنامج بنجاح");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة الرسالة للبرنامج: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                    }
                });

                // حفظ الرسالة كرسالة معلقة للمرات القادمة (في حالة إغلاق البرنامج)
                try
                {
                    SavePendingMessage(messageSource, messageText, messageDate, chatId, chatTitle, chatType.ToString(), isFromGroupOrChannel);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الرسالة المعلقة: {ex.Message}");
                }

                // إرسال رد تأكيد فقط للمحادثات الخاصة (ليس للقنوات والمجموعات)
                if (!isFromGroupOrChannel)
                {
                    try
                    {
                        // إنشاء زر حذف مع معرف الرسالة
                        var inlineKeyboard = new InlineKeyboardMarkup(new[]
                        {
                            new[]
                            {
                                InlineKeyboardButton.WithCallbackData("🗑️ حذف الرسالة", $"delete_{messageId}")
                            }
                        });

                        await botClient.SendTextMessageAsync(
                            chatId: chatId,
                            text: $"✅ تم استلام رسالتك وإضافتها إلى نظام Inzo IB\n\n📝 الرسالة: {messageText}\n\n🆔 Chat ID: `{chatId}`\n🕐 الوقت: {DateTime.Now:HH:mm:ss}",
                            parseMode: TelegramTypes.Enums.ParseMode.Markdown,
                            replyMarkup: inlineKeyboard,
                            cancellationToken: cancellationToken);

                        System.Diagnostics.Debug.WriteLine("✅ تم إرسال رد التأكيد مع زر الحذف للمرسل");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في إرسال رد التأكيد: {ex.Message}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"📢 تم استلام رسالة من {messageSource} - لا يتم إرسال رد تأكيد للقنوات والمجموعات");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الرسالة النصية: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        private async Task HandleCallbackQuery(ITelegramBotClient botClient, TelegramTypes.CallbackQuery callbackQuery, CancellationToken cancellationToken)
        {
            try
            {
                var callbackData = callbackQuery.Data;
                var chatId = callbackQuery.Message.Chat.Id;
                var messageId = callbackQuery.Message.MessageId;

                System.Diagnostics.Debug.WriteLine($"🔘 تم الضغط على زر: {callbackData}");

                // التحقق من أن الضغط على زر حذف
                if (callbackData.StartsWith("delete_"))
                {
                    var contentMessageId = callbackData.Substring(7); // إزالة "delete_" من البداية
                    System.Diagnostics.Debug.WriteLine($"🔍 محاولة حذف رسالة بمعرف: {contentMessageId}");

                    // حذف الرسالة من البرنامج
                    bool deleted = false;
                    Dispatcher.Invoke(() =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"📋 عدد الرسائل قبل الحذف: {contentItems?.Count ?? 0}");
                            deleted = DeleteTelegramMessageFromInzoIB(contentMessageId);
                            System.Diagnostics.Debug.WriteLine($"📋 عدد الرسائل بعد الحذف: {contentItems?.Count ?? 0}");
                            System.Diagnostics.Debug.WriteLine($"✅ نتيجة الحذف: {deleted}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف الرسالة من البرنامج: {ex.Message}");
                            System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                        }
                    });

                    // إرسال رد على الضغط على الزر
                    if (deleted)
                    {
                        await botClient.AnswerCallbackQueryAsync(
                            callbackQueryId: callbackQuery.Id,
                            text: "✅ تم حذف الرسالة من النظام بنجاح",
                            showAlert: true,
                            cancellationToken: cancellationToken);

                        // تعديل الرسالة لإزالة الزر وإضافة نص الحذف
                        await botClient.EditMessageTextAsync(
                            chatId: chatId,
                            messageId: messageId,
                            text: $"{callbackQuery.Message.Text}\n\n🗑️ تم حذف هذه الرسالة من النظام",
                            cancellationToken: cancellationToken);
                    }
                    else
                    {
                        await botClient.AnswerCallbackQueryAsync(
                            callbackQueryId: callbackQuery.Id,
                            text: "❌ لم يتم العثور على الرسالة أو حدث خطأ في الحذف",
                            showAlert: true,
                            cancellationToken: cancellationToken);
                    }
                }
                else
                {
                    // رد افتراضي للأزرار غير المعروفة
                    await botClient.AnswerCallbackQueryAsync(
                        callbackQueryId: callbackQuery.Id,
                        text: "⚠️ أمر غير معروف",
                        cancellationToken: cancellationToken);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الضغط على الزر: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        private Task HandlePollingErrorAsync(ITelegramBotClient botClient, Exception exception, CancellationToken cancellationToken)
        {
            var errorMessage = exception switch
            {
                ApiRequestException apiRequestException
                    => $"Telegram API Error:\n[{apiRequestException.ErrorCode}]\n{apiRequestException.Message}",
                _ => exception.ToString()
            };

            System.Diagnostics.Debug.WriteLine($"❌ خطأ في بوت التيليجرام: {errorMessage}");
            return Task.CompletedTask;
        }

        private string AddTelegramMessageToInzoIB(string userName, string messageText, DateTime messageDate, bool isSyncMessage = false, int telegramMessageId = 0, long telegramChatId = 0)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء إضافة رسالة من {userName} إلى قسم Inzo IB");

                // التأكد من وجود قائمة المحتوى
                if (contentItems == null)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ قائمة المحتوى فارغة، إنشاء قائمة جديدة");
                    contentItems = new List<ContentItem>();
                }

                // إنشاء عنصر محتوى جديد للرسالة
                var telegramMessage = new ContentItem
                {
                    Section = "INZO IB", // تصحيح اسم القسم ليطابق الزر
                    Title = "", // العنوان فارغ لرسائل التيليجرام
                    Content = messageText, // النص فقط بدون معلومات إضافية
                    CreatedDate = messageDate,
                    AttachedFiles = new List<string>(), // قائمة فارغة للملفات المرفقة
                    IsTelegramMessage = true, // تمييز رسائل التيليجرام
                    TelegramSender = userName, // حفظ اسم المرسل منفصلاً
                    TelegramMessageId = telegramMessageId, // معرف الرسالة في تيليجرام
                    TelegramChatId = telegramChatId // معرف المحادثة/القناة في تيليجرام
                };

                System.Diagnostics.Debug.WriteLine($"📝 تم إنشاء عنصر المحتوى بمعرف: {telegramMessage.Id}");

                // إضافة الرسالة إلى قائمة المحتوى
                contentItems.Insert(0, telegramMessage); // إضافة في المقدمة
                System.Diagnostics.Debug.WriteLine($"📋 تم إضافة الرسالة إلى القائمة. إجمالي العناصر: {contentItems.Count}");

                // إضافة الرسالة إلى قاموس التتبع للحذف
                if (telegramMessageId > 0 && telegramChatId != 0)
                {
                    var messageKey = $"{telegramChatId}_{telegramMessageId}";
                    telegramMessageMap[messageKey] = telegramMessage;
                    System.Diagnostics.Debug.WriteLine($"🔗 تم ربط الرسالة بمعرف تيليجرام: {messageKey}");
                }

                // حفظ البيانات
                try
                {
                    SaveData();
                    System.Diagnostics.Debug.WriteLine("💾 تم حفظ البيانات بنجاح");
                }
                catch (Exception saveEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ البيانات: {saveEx.Message}");
                }

                // تحديث العرض إذا كان المستخدم في قسم INZO IB
                System.Diagnostics.Debug.WriteLine($"🔍 القسم الحالي: '{currentSection}'");
                if (currentSection == "INZO IB") // تصحيح اسم القسم
                {
                    System.Diagnostics.Debug.WriteLine("🔄 المستخدم في قسم INZO IB، تحديث العرض");
                    try
                    {
                        LoadCurrentSectionContent();
                        System.Diagnostics.Debug.WriteLine("✅ تم تحديث العرض بنجاح");
                    }
                    catch (Exception loadEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث العرض: {loadEx.Message}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"ℹ️ المستخدم في قسم '{currentSection}'، لن يتم تحديث العرض");
                }

                // إجبار تحديث العرض بغض النظر عن القسم الحالي (للاختبار)
                try
                {
                    System.Diagnostics.Debug.WriteLine("🔄 إجبار تحديث العرض للاختبار");
                    var inzoItems = contentItems.Where(item => item.Section == "INZO IB").ToList(); // تصحيح اسم القسم
                    System.Diagnostics.Debug.WriteLine($"📊 عدد عناصر INZO IB: {inzoItems.Count}");

                    if (inzoItems.Any())
                    {
                        var latestItem = inzoItems.First();
                        System.Diagnostics.Debug.WriteLine($"📝 آخر عنصر: {latestItem.Title}");
                    }
                }
                catch (Exception debugEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في التشخيص: {debugEx.Message}");
                }

                // تحديث شريط الحالة
                StatusText.Text = $"📱 رسالة جديدة من {userName} في قسم Inzo IB - {DateTime.Now:HH:mm:ss}";

                System.Diagnostics.Debug.WriteLine($"✅ تم إضافة رسالة التيليجرام إلى قسم Inzo IB بنجاح");

                // مزامنة الرسالة مع الأجهزة الأخرى (فقط إذا لم تكن رسالة مزامنة)
                if (!isSyncMessage && networkSyncManager != null)
                {
                    try
                    {
                        _ = Task.Run(async () => await networkSyncManager.BroadcastMessageAsync(userName, messageText, messageDate));
                        System.Diagnostics.Debug.WriteLine("🌐 تم إرسال الرسالة للمزامنة مع الأجهزة الأخرى");
                    }
                    catch (Exception syncEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة الرسالة: {syncEx.Message}");
                    }
                }

                // إرجاع معرف الرسالة
                return telegramMessage.Id;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة رسالة التيليجرام: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                // تحديث شريط الحالة بالخطأ
                StatusText.Text = $"❌ خطأ في إضافة رسالة من {userName}";

                // إرجاع null في حالة الخطأ
                return null;
            }
        }

        /// <summary>
        /// حذف رسالة تيليجرام من قسم Inzo IB بناءً على المعرف
        /// </summary>
        /// <param name="messageId">معرف الرسالة المراد حذفها</param>
        /// <returns>true إذا تم الحذف بنجاح، false إذا لم يتم العثور على الرسالة</returns>
        private bool DeleteTelegramMessageFromInzoIB(string messageId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء حذف رسالة التيليجرام بمعرف: {messageId}");

                if (contentItems == null || string.IsNullOrEmpty(messageId))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ قائمة المحتوى فارغة أو معرف الرسالة غير صحيح");
                    return false;
                }

                // البحث عن الرسالة بالمعرف
                var messageToDelete = contentItems.FirstOrDefault(item =>
                    item.Id == messageId &&
                    item.IsTelegramMessage &&
                    item.Section == "INZO IB");

                if (messageToDelete == null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على رسالة التيليجرام بمعرف: {messageId}");
                    return false;
                }

                // حذف الرسالة من القائمة
                contentItems.Remove(messageToDelete);
                System.Diagnostics.Debug.WriteLine($"✅ تم حذف رسالة التيليجرام من {messageToDelete.TelegramSender}");

                // حذف الرسالة من قاموس التتبع أيضاً
                if (messageToDelete.TelegramMessageId > 0 && messageToDelete.TelegramChatId != 0)
                {
                    var messageKey = $"{messageToDelete.TelegramChatId}_{messageToDelete.TelegramMessageId}";
                    if (telegramMessageMap.ContainsKey(messageKey))
                    {
                        telegramMessageMap.Remove(messageKey);
                        System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف الرسالة من قاموس التتبع: {messageKey}");
                    }
                }

                // حفظ البيانات
                try
                {
                    SaveData();
                    System.Diagnostics.Debug.WriteLine("💾 تم حفظ البيانات بعد الحذف");
                }
                catch (Exception saveEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ البيانات بعد الحذف: {saveEx.Message}");
                }

                // تحديث العرض إذا كان المستخدم في قسم INZO IB
                if (currentSection == "INZO IB")
                {
                    try
                    {
                        LoadCurrentSectionContent();
                        System.Diagnostics.Debug.WriteLine("✅ تم تحديث العرض بعد الحذف");
                    }
                    catch (Exception loadEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث العرض بعد الحذف: {loadEx.Message}");
                    }
                }

                // تحديث شريط الحالة
                StatusText.Text = $"🗑️ تم حذف رسالة من {messageToDelete.TelegramSender} - {DateTime.Now:HH:mm:ss}";

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف رسالة التيليجرام: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                return false;
            }
        }

        #endregion

        #region دالة تحديث ألوان أزرار الأقسام

        /// <summary>
        /// دالة منفصلة لتحديث ألوان أزرار الأقسام
        /// تجعل القسم النشط بلون مميز وباقي الأقسام باللون العادي
        /// </summary>
        private void UpdateSectionButtonColors(Button activeButton)
        {
            try
            {
                // اللون العادي للأزرار غير النشطة
                var normalColor = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)); // #161642

                // اللون المميز للزر النشط (أخضر داكن)
                var activeColor = new SolidColorBrush(Color.FromRgb(0x2E, 0x7D, 0x32)); // #2E7D32

                // قائمة جميع أزرار الأقسام
                var sectionButtons = new List<Button>
                {
                    BtnTawdeeh, BtnRodod, BtnOmala, BtnFaezeen, BtnMosabakat,
                    BtnWokala, BtnSahbWaEeeda, BtnInzoIB, BtnNasekh,
                    BtnTaaweedWaTadkeek, BtnManshoorat, BtnSowar, BtnMeeting
                };

                // إعادة تعيين جميع الأزرار للون العادي
                foreach (var btn in sectionButtons)
                {
                    if (btn != null)
                    {
                        btn.Background = normalColor;
                    }
                }

                // تعيين اللون المميز للزر النشط
                if (activeButton != null)
                {
                    activeButton.Background = activeColor;
                }

                System.Diagnostics.Debug.WriteLine($"تم تحديث لون زر القسم النشط: {currentSection}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث ألوان أزرار الأقسام: {ex.Message}");
            }
        }

        #endregion

        private void LoadCurrentSectionContent()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 تحميل محتوى القسم: {currentSection}");

                if (ContentPanel == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ ContentPanel غير موجود");
                    return;
                }

                if (contentItems == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ contentItems غير موجود");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"📊 إجمالي العناصر: {contentItems.Count}");

                ContentPanel.Children.Clear();
                ClearSelection();

                // الحصول على محتوى القسم الحالي
                var sectionItems = contentItems.FindAll(item => item.Section == currentSection);
                System.Diagnostics.Debug.WriteLine($"📋 عناصر القسم '{currentSection}': {sectionItems.Count}");

                if (sectionItems.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لا توجد عناصر في القسم '{currentSection}'");
                    // عرض رسالة عدم وجود محتوى
                    ShowEmptyMessage();
                    return;
                }

                // عرض كل عنصر محتوى
                foreach (var item in sectionItems)
                {
                    CreateContentItemUI(item);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم عرض {sectionItems.Count} عنصر في القسم '{currentSection}'");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل المحتوى: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                // محاولة عرض رسالة خطأ بسيطة
                if (StatusText != null)
                {
                    StatusText.Text = "❌ خطأ في تحميل المحتوى";
                }
            }
        }

        private void ShowEmptyMessage()
        {
            var border = new Border
            {
                Background = Brushes.LightYellow,
                BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Padding = new Thickness(20)
            };

            var stackPanel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };

            var emptyIcon = new TextBlock
            {
                Text = "📝",
                FontSize = 48,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var emptyTitle = new TextBlock
            {
                Text = $"لا يوجد محتوى في قسم {currentSection}",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var emptyMessage = new TextBlock
            {
                Text = "اضغط على زر 'إضافة جديد' لإضافة محتوى جديد إلى هذا القسم",
                FontSize = 14,
                Foreground = Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            stackPanel.Children.Add(emptyIcon);
            stackPanel.Children.Add(emptyTitle);
            stackPanel.Children.Add(emptyMessage);

            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void CreateContentItemUI(ContentItem item)
        {
            // إذا كان شريط تقسيم، إنشاء عنصر شريط
            if (item.IsDivider)
            {
                CreateDividerUI(item);
                return;
            }

            // إذا كانت رسالة تيليجرام، إنشاء واجهة خاصة
            if (item.IsTelegramMessage)
            {
                CreateTelegramMessageUI(item);
                return;
            }

            var border = new Border
            {
                Background = Brushes.White,
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(15),
                Tag = item,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // إضافة حدث النقر للتحديد
            border.MouseLeftButtonUp += SelectContentItem;

            var stackPanel = new StackPanel();

            // إضافة العنوان فقط إذا لم يكن فارغاً
            if (!string.IsNullOrWhiteSpace(item.Title))
            {
                var titleBlock = new TextBlock
                {
                    Text = item.Title,
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                    Margin = new Thickness(0, 0, 0, 10)
                };
                stackPanel.Children.Add(titleBlock);
            }

            // عرض الملف المرفق أو المحتوى
            if (item.AttachedFiles.Count > 0)
            {
                // عرض الملف المرفق
                var attachedFile = item.AttachedFiles.First();
                var fileDisplayElement = CreateFileDisplayElement(attachedFile, item);
                stackPanel.Children.Add(fileDisplayElement);
            }
            else if (!string.IsNullOrWhiteSpace(item.Content))
            {
                // عرض المحتوى النصي فقط إذا لم يكن فارغاً
                var contentBlock = new TextBlock
                {
                    Text = item.Content,
                    FontSize = 14,
                    // لون أغمق عندما لا يوجد عنوان، لون عادي عندما يوجد عنوان
                    Foreground = string.IsNullOrWhiteSpace(item.Title) ?
                        new SolidColorBrush(Color.FromRgb(0x2C, 0x2C, 0x2C)) : // لون أغمق (رمادي داكن جداً)
                        Brushes.DarkGray, // اللون العادي
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(0, 0, 0, 10)
                };
                stackPanel.Children.Add(contentBlock);
            }
            else if (string.IsNullOrWhiteSpace(item.Title))
            {
                // إذا لم يكن هناك عنوان ولا محتوى، عرض رسالة توضيحية
                var placeholderBlock = new TextBlock
                {
                    Text = "📝 عنصر فارغ - يمكنك إضافة محتوى أو ملف مرفق",
                    FontSize = 12,
                    Foreground = Brushes.Gray,
                    FontStyle = FontStyles.Italic,
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(0, 0, 0, 10)
                };
                stackPanel.Children.Add(placeholderBlock);
            }



            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void CreateTelegramMessageUI(ContentItem item)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0xF8, 0xFF, 0xF8)), // خلفية خضراء فاتحة
                BorderBrush = new SolidColorBrush(Color.FromRgb(0x4C, 0xAF, 0x50)), // لون أخضر للتمييز
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(15),
                Tag = item,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // إضافة حدث النقر للتحديد
            border.MouseLeftButtonUp += SelectContentItem;

            var mainStackPanel = new StackPanel();

            // المحتوى النصي (قابل للتحديد والنسخ)
            if (!string.IsNullOrWhiteSpace(item.Content))
            {
                var contentTextBox = new TextBox
                {
                    Text = item.Content,
                    FontSize = 14,
                    Foreground = new SolidColorBrush(Color.FromRgb(0x2C, 0x2C, 0x2C)),
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(0, 0, 0, 10),
                    IsReadOnly = true, // للقراءة فقط لكن قابل للتحديد والنسخ
                    BorderThickness = new Thickness(0), // بدون حدود
                    Background = Brushes.Transparent, // خلفية شفافة
                    IsTabStop = false // لا يتم التركيز عليه بالـ Tab
                };
                mainStackPanel.Children.Add(contentTextBox);
            }

            // معلومات المرسل والتاريخ (غير قابلة للتحديد أو النسخ)
            var infoPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 5, 0, 0)
            };

            // اسم المرسل
            var senderLabel = new Label
            {
                Content = $"👤 {item.TelegramSender}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(-5, 0, 10, -5), // تعديل المسافات
                Padding = new Thickness(0) // بدون padding داخلي
            };
            infoPanel.Children.Add(senderLabel);

            // التاريخ
            var dateLabel = new Label
            {
                Content = $"📅 {item.CreatedDate:yyyy-MM-dd HH:mm:ss}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(0, 0, 0, -5), // تعديل المسافات
                Padding = new Thickness(0) // بدون padding داخلي
            };
            infoPanel.Children.Add(dateLabel);

            mainStackPanel.Children.Add(infoPanel);

            // إضافة لوحة الأزرار
            var actionPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 5, 0, 0),
                HorizontalAlignment = HorizontalAlignment.Right
            };

            // زر حذف الرسالة
            var deleteButton = new Button
            {
                Content = "🗑️ حذف الرسالة",
                FontSize = 10,
                Background = new SolidColorBrush(Color.FromRgb(0xFF, 0x44, 0x44)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(8, 4, 8, 4),
                Margin = new Thickness(0, 0, 10, 0),
                Cursor = System.Windows.Input.Cursors.Hand,
                Tag = item
            };
            deleteButton.Click += DeleteTelegramMessage_Click;
            actionPanel.Children.Add(deleteButton);

            // مؤشر نوع الرسالة
            var typeLabel = new Label
            {
                Content = "📱 رسالة تيليجرام",
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(0x4C, 0xAF, 0x50)),
                FontStyle = FontStyles.Italic,
                Padding = new Thickness(0),
                Margin = new Thickness(0, 0, 0, -5)
            };
            actionPanel.Children.Add(typeLabel);

            mainStackPanel.Children.Add(actionPanel);

            border.Child = mainStackPanel;
            ContentPanel.Children.Add(border);
        }

        /// <summary>
        /// معالج حدث حذف رسالة التيليجرام
        /// </summary>
        private async void DeleteTelegramMessage_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is ContentItem item)
                {
                    // تأكيد الحذف
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف هذه الرسالة؟\n\nالمرسل: {item.TelegramSender}\nالمحتوى: {item.Content.Substring(0, Math.Min(50, item.Content.Length))}...\n\nلا يمكن التراجع عن هذا الإجراء.",
                        "تأكيد حذف رسالة التيليجرام",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        // حذف الرسالة من القائمة
                        contentItems.Remove(item);

                        // حذف من قاموس التتبع
                        if (item.TelegramMessageId > 0 && item.TelegramChatId != 0)
                        {
                            var messageKey = $"{item.TelegramChatId}_{item.TelegramMessageId}";
                            telegramMessageMap.Remove(messageKey);
                            System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف الرسالة من قاموس التتبع: {messageKey}");
                        }

                        // حفظ البيانات
                        SaveData();

                        // تحديث الواجهة
                        LoadCurrentSectionContent();

                        // تحديث شريط الحالة
                        StatusText.Text = $"🗑️ تم حذف رسالة من {item.TelegramSender} - {DateTime.Now:HH:mm:ss}";

                        System.Diagnostics.Debug.WriteLine($"✅ تم حذف رسالة التيليجرام من {item.TelegramSender}");

                        // إرسال إشعار للمرسل (اختياري)
                        if (telegramBot != null && item.TelegramChatId != 0)
                        {
                            try
                            {
                                await telegramBot.SendTextMessageAsync(
                                    chatId: item.TelegramChatId,
                                    text: "🗑️ تم حذف رسالتك من نظام Inzo IB",
                                    cancellationToken: telegramCancellationToken.Token);

                                System.Diagnostics.Debug.WriteLine("📤 تم إرسال إشعار الحذف للمرسل");
                            }
                            catch (Exception notifyEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم إرسال إشعار الحذف: {notifyEx.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف رسالة التيليجرام: {ex.Message}");
                StatusText.Text = $"❌ خطأ في حذف الرسالة: {ex.Message}";

                MessageBox.Show(
                    $"حدث خطأ أثناء حذف الرسالة:\n{ex.Message}",
                    "خطأ في الحذف",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void CreateDividerUI(ContentItem item)
        {
            try
            {
                var dividerContainer = new StackPanel
                {
                    Margin = new Thickness(0, 10, 0, 20),
                    Tag = item
                };

                // إضافة حدث النقر للتحديد
                dividerContainer.MouseLeftButtonUp += SelectContentItem;

            // الشريط الرئيسي
            var dividerBorder = new Border
            {
                Height = 3,
                Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                CornerRadius = new CornerRadius(1.5),
                Margin = new Thickness(0, 5, 0, 5)
            };

            // نص الشريط إذا وجد
            if (!string.IsNullOrWhiteSpace(item.DividerText))
            {
                var textContainer = new Border
                {
                    Background = Brushes.White,
                    BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                    BorderThickness = new Thickness(2),
                    CornerRadius = new CornerRadius(15),
                    Padding = new Thickness(15, 5, 15, 5),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 5)
                };

                var textBlock = new TextBlock
                {
                    Text = item.DividerText,
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                textContainer.Child = textBlock;
                dividerContainer.Children.Add(textContainer);
            }

                dividerContainer.Children.Add(dividerBorder);

                ContentPanel.Children.Add(dividerContainer);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء شريط التقسيم: {ex.Message}");

                // إنشاء شريط بسيط في حالة الخطأ
                var simpleDivider = new Border
                {
                    Height = 2,
                    Background = Brushes.Gray,
                    Margin = new Thickness(0, 10, 0, 10),
                    Tag = item
                };
                simpleDivider.MouseLeftButtonUp += SelectContentItem;
                ContentPanel.Children.Add(simpleDivider);
            }
        }

        private FrameworkElement CreateFileDisplayElement(string filePath, ContentItem item)
        {
            if (IsImageFile(filePath) && File.Exists(filePath))
            {
                // عرض الصورة
                return CreateImageDisplay(filePath, item);
            }
            else
            {
                // عرض معلومات الملف
                return CreateFileInfoDisplay(filePath, item);
            }
        }

        private FrameworkElement CreateImageDisplay(string filePath, ContentItem item)
        {
            var border = new Border
            {
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Background = Brushes.White,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // استخدام Grid لضمان أبعاد ثابتة للصورة
            var grid = new Grid
            {
                Margin = new Thickness(5)
            };

            // تعريف الأعمدة: النص على اليسار، الصورة على اليمين
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // النص
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(220, GridUnitType.Pixel) }); // الصورة بعرض ثابت

            try
            {
                // نص المحتوى في العمود الأول (اليسار)
                if (!string.IsNullOrWhiteSpace(item.Content))
                {
                    var contentText = new TextBlock
                    {
                        Text = item.Content,
                        FontSize = 14,
                        // لون أغمق عندما لا يوجد عنوان، لون عادي عندما يوجد عنوان
                        Foreground = string.IsNullOrWhiteSpace(item.Title) ?
                            new SolidColorBrush(Color.FromRgb(0x2C, 0x2C, 0x2C)) : // لون أغمق
                            Brushes.DarkGray, // اللون العادي
                        TextWrapping = TextWrapping.Wrap,
                        Margin = new Thickness(5, 5, 10, 5),
                        VerticalAlignment = VerticalAlignment.Top
                    };
                    Grid.SetColumn(contentText, 0);
                    grid.Children.Add(contentText);
                }

                // إنشاء الصورة في العمود الثاني (اليمين) بأبعاد ثابتة
                var image = new Image
                {
                    Width = 200,
                    Height = 150,
                    Stretch = Stretch.UniformToFill,
                    Margin = new Thickness(5),
                    VerticalAlignment = VerticalAlignment.Top,
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                Grid.SetColumn(image, 1);

                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(filePath);
                bitmap.DecodePixelWidth = 200;
                bitmap.DecodePixelHeight = 150;
                bitmap.EndInit();

                image.Source = bitmap;

                // إضافة الصورة إلى العمود الثاني
                grid.Children.Add(image);


            }
            catch
            {
                var errorText = new TextBlock
                {
                    Text = $"❌ خطأ في تحميل الصورة: {Path.GetFileName(filePath)}",
                    FontSize = 14,
                    Foreground = Brushes.Red,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(10)
                };
                Grid.SetColumnSpan(errorText, 2); // يمتد عبر العمودين
                grid.Children.Add(errorText);
            }

            border.Child = grid;

            // إضافة حدث النقر المزدوج لفتح نافذة إدارة الملف
            border.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ClickCount == 2)
                {
                    OpenFileManagementWindow(item);
                }
            };

            return border;
        }

        private FrameworkElement CreateFileInfoDisplay(string filePath, ContentItem item)
        {
            var border = new Border
            {
                BorderBrush = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Background = Brushes.White,
                Padding = new Thickness(15),
                Cursor = System.Windows.Input.Cursors.Hand
            };

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var infoPanel = new StackPanel();

            // نص المحتوى إذا وجد (فقط)
            if (!string.IsNullOrWhiteSpace(item.Content))
            {
                var contentText = new TextBlock
                {
                    Text = item.Content,
                    FontSize = 14,
                    // لون أغمق عندما لا يوجد عنوان، لون عادي عندما يوجد عنوان
                    Foreground = string.IsNullOrWhiteSpace(item.Title) ?
                        new SolidColorBrush(Color.FromRgb(0x2C, 0x2C, 0x2C)) : // لون أغمق
                        Brushes.DarkGray, // اللون العادي
                    TextWrapping = TextWrapping.Wrap,
                    MaxWidth = 300
                };
                infoPanel.Children.Add(contentText);
            }

            // أيقونة الملف
            var fileIcon = new TextBlock
            {
                Text = GetFileIcon(filePath),
                FontSize = 40,
                Margin = new Thickness(15, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(infoPanel);
            stackPanel.Children.Add(fileIcon);

            border.Child = stackPanel;

            // إضافة حدث النقر المزدوج لفتح نافذة إدارة الملف
            border.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ClickCount == 2)
                {
                    OpenFileManagementWindow(item);
                }
            };

            return border;
        }

        private void OpenFileManagementWindow(ContentItem item)
        {
            var fileWindow = new FileAttachmentWindow(item.Title, item.AttachedFiles);
            if (fileWindow.ShowDialog() == true)
            {
                // تحديث الملفات المرفقة
                item.AttachedFiles = fileWindow.AttachedFiles;
                SaveData();
                LoadContent(currentSection);
            }
        }

        private bool IsImageFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return extension == ".jpg" || extension == ".jpeg" || extension == ".png" ||
                   extension == ".gif" || extension == ".bmp" || extension == ".tiff";
        }

        private string GetFileIcon(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return extension switch
            {
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".ppt" or ".pptx" => "📋",
                ".txt" => "📃",
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => "🖼️",
                ".mp4" or ".avi" or ".mkv" or ".mov" => "🎥",
                ".mp3" or ".wav" or ".flac" => "🎵",
                ".zip" or ".rar" or ".7z" => "📦",
                _ => "📁"
            };
        }

        private string GetFileSize(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    var sizeInBytes = fileInfo.Length;

                    if (sizeInBytes < 1024)
                        return $"{sizeInBytes} بايت";
                    else if (sizeInBytes < 1024 * 1024)
                        return $"{sizeInBytes / 1024:F1} كيلوبايت";
                    else if (sizeInBytes < 1024 * 1024 * 1024)
                        return $"{sizeInBytes / (1024 * 1024):F1} ميجابايت";
                    else
                        return $"{sizeInBytes / (1024 * 1024 * 1024):F1} جيجابايت";
                }
                else
                {
                    return "غير موجود";
                }
            }
            catch
            {
                return "غير معروف";
            }
        }

        private void SaveData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 محاولة حفظ {contentItems?.Count ?? 0} عنصر");

                // احفظ في JSON المحلي كنسخة احتياطية دائماً أولاً
                SaveDataToLocalJson();

                // إذا كان Firestore مفعل، احفظ في Firestore مع المزامنة الذكية
                if (useFirestore && firestoreManager != null)
                {
                    System.Diagnostics.Debug.WriteLine("🔥 Firestore مفعل - حفظ مع المزامنة الذكية");
                    _ = Task.Run(async () => await SaveDataToFirestoreAsync());
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("📁 حفظ محلي فقط - Firestore غير مفعل");
                }

                // تفعيل المزامنة الشاملة للبيانات المحدثة
                if (universalSyncManager != null && contentItems != null)
                {
                    _ = Task.Run(async () => await universalSyncManager.SyncDataUniversallyAsync(contentItems, "UPDATE"));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ البيانات في Firestore
        /// </summary>
        private async Task SaveDataToFirestoreAsync()
        {
            try
            {
                if (firestoreManager != null && contentItems != null)
                {
                    var success = await firestoreManager.SaveDataAsync(contentItems);
                    if (success)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {contentItems.Count} عنصر في Firestore");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("❌ فشل في حفظ البيانات في Firestore");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ البيانات في Firestore: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ البيانات في JSON المحلي
        /// </summary>
        private void SaveDataToLocalJson()
        {
            try
            {
                // التأكد من وجود مجلد البيانات
                if (!Directory.Exists(dataFolderPath))
                {
                    Directory.CreateDirectory(dataFolderPath);
                    System.Diagnostics.Debug.WriteLine($"تم إنشاء مجلد البيانات: {dataFolderPath}");
                }

                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(contentItems, options);
                File.WriteAllText(dataFilePath, json);

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {contentItems.Count} عنصر في JSON المحلي: {dataFilePath}");
                System.Diagnostics.Debug.WriteLine($"حجم البيانات المحفوظة: {json.Length} حرف");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ البيانات في JSON المحلي: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"مسار الحفظ: {dataFilePath}");
            }
        }

        private void LoadContent(string section)
        {
            try
            {
                ContentPanel.Children.Clear();

                if (contentItems == null)
                {
                    LoadData();
                }

                var sectionItems = contentItems.Where(item => item.Section == section).ToList();

                if (sectionItems.Count == 0)
                {
                    var noContentMessage = new TextBlock
                    {
                        Text = $"لا يوجد محتوى في قسم '{section}' حتى الآن",
                        FontSize = 16,
                        Foreground = Brushes.Gray,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(20)
                    };

                    var border = new Border
                    {
                        Background = Brushes.White,
                        BorderBrush = Brushes.LightGray,
                        BorderThickness = new Thickness(1),
                        CornerRadius = new CornerRadius(5),
                        Margin = new Thickness(0, 0, 0, 15),
                        Padding = new Thickness(20),
                        Child = noContentMessage
                    };

                    ContentPanel.Children.Add(border);
                    return;
                }

                foreach (var item in sectionItems)
                {
                    CreateContentItemUI(item);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المحتوى: {ex.Message}");
            }
        }

        private void LoadData()
        {
            try
            {
                // تحميل البيانات المحلية أولاً دائماً
                System.Diagnostics.Debug.WriteLine("📁 تحميل البيانات المحلية أولاً...");
                LoadDataFromLocalJson();

                // إذا كان Firestore مفعل، استخدم المزامنة الذكية
                if (useFirestore && firestoreManager != null)
                {
                    System.Diagnostics.Debug.WriteLine("🔄 Firestore مفعل - سيتم المزامنة في الخلفية");
                    _ = Task.Run(async () => await LoadDataFromFirestoreAsync());
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("📁 استخدام البيانات المحلية فقط");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل البيانات: {ex.Message}");
                contentItems = new List<ContentItem>();
            }
        }

        /// <summary>
        /// تحميل البيانات من JSON المحلي
        /// </summary>
        private void LoadDataFromLocalJson()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"📁 محاولة تحميل البيانات من JSON المحلي: {dataFilePath}");

                if (File.Exists(dataFilePath))
                {
                    var json = File.ReadAllText(dataFilePath);
                    System.Diagnostics.Debug.WriteLine($"📄 تم قراءة الملف، حجم البيانات: {json.Length} حرف");

                    if (!string.IsNullOrEmpty(json))
                    {
                        contentItems = JsonSerializer.Deserialize<List<ContentItem>>(json) ?? new List<ContentItem>();
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {contentItems.Count} عنصر من JSON المحلي");

                        // عرض تفاصيل العناصر المحملة
                        foreach (var item in contentItems.Take(3))
                        {
                            System.Diagnostics.Debug.WriteLine($"   📝 {item.Section}: {item.Title} ({item.CreatedDate:yyyy-MM-dd})");
                        }

                        if (contentItems.Count > 3)
                        {
                            System.Diagnostics.Debug.WriteLine($"   ... و {contentItems.Count - 3} عنصر آخر");
                        }
                    }
                    else
                    {
                        contentItems = new List<ContentItem>();
                        System.Diagnostics.Debug.WriteLine("⚠️ ملف JSON فارغ");
                    }
                }
                else
                {
                    contentItems = new List<ContentItem>();
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على ملف البيانات في: {dataFilePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل البيانات من JSON المحلي: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ مسار الملف: {dataFilePath}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                contentItems = new List<ContentItem>();
            }
        }

        private void SelectContentItem(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            var element = sender as FrameworkElement;
            var item = element?.Tag as ContentItem;

            if (item != null)
            {
                // إلغاء تحديد العناصر السابقة
                ClearSelection();

                // تحديد العنصر الحالي
                selectedItem = item;

                if (item.IsDivider)
                {
                    // تحديد الشريط
                    var stackPanel = sender as StackPanel;
                    if (stackPanel != null)
                    {
                        try
                        {
                            stackPanel.Background = new SolidColorBrush(Color.FromRgb(0xE3, 0xF2, 0xFD));

                            // إضافة حدود حول الشريط بأمان
                            if (stackPanel.Children.Count > 0)
                            {
                                foreach (UIElement child in stackPanel.Children)
                                {
                                    if (child is Border borderChild && borderChild.Height == 3)
                                    {
                                        borderChild.BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42));
                                        borderChild.BorderThickness = new Thickness(1);
                                        break;
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"خطأ في تحديد شريط التقسيم: {ex.Message}");
                        }
                    }

                    // تفعيل أزرار محدودة للشريط
                    if (TaskEditButton != null) TaskEditButton.IsEnabled = false;
                    if (TaskCopyButton != null) TaskCopyButton.IsEnabled = false;
                    if (TaskDeleteButton != null) TaskDeleteButton.IsEnabled = true;
                    if (TaskFilesButton != null) TaskFilesButton.IsEnabled = false;
                    if (TaskMoveUpButton != null) TaskMoveUpButton.IsEnabled = true;
                    if (TaskMoveDownButton != null) TaskMoveDownButton.IsEnabled = true;

                    StatusText.Text = $"✅ تم تحديد شريط التقسيم: {(string.IsNullOrWhiteSpace(item.DividerText) ? "بدون نص" : item.DividerText)}";
                }
                else
                {
                    // تحديد المحتوى العادي
                    var border = sender as Border;
                    if (border != null)
                    {
                        border.BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42));
                        border.BorderThickness = new Thickness(3);
                        border.Background = new SolidColorBrush(Color.FromRgb(0xE3, 0xF2, 0xFD));
                    }

                    // تفعيل جميع أزرار شريط المهام
                    if (TaskEditButton != null) TaskEditButton.IsEnabled = true;
                    if (TaskCopyButton != null) TaskCopyButton.IsEnabled = true;
                    if (TaskDeleteButton != null) TaskDeleteButton.IsEnabled = true;
                    if (TaskFilesButton != null) TaskFilesButton.IsEnabled = true;
                    if (TaskMoveUpButton != null) TaskMoveUpButton.IsEnabled = true;
                    if (TaskMoveDownButton != null) TaskMoveDownButton.IsEnabled = true;

                    StatusText.Text = $"✅ تم تحديد: {item.Title}";
                }
            }
        }

        private void ClearSelection()
        {
            try
            {
                selectedItem = null;

                // إلغاء تحديد جميع العناصر
                if (ContentPanel != null)
                {
                    foreach (UIElement element in ContentPanel.Children)
                    {
                        if (element is Border border)
                        {
                            border.BorderBrush = Brushes.LightGray;
                            border.BorderThickness = new Thickness(1);
                            border.Background = Brushes.White;
                        }
                    }
                }

                // تعطيل أزرار شريط المهام
                if (TaskEditButton != null) TaskEditButton.IsEnabled = false;
                if (TaskCopyButton != null) TaskCopyButton.IsEnabled = false;
                if (TaskDeleteButton != null) TaskDeleteButton.IsEnabled = false;
                if (TaskFilesButton != null) TaskFilesButton.IsEnabled = false;
                if (TaskMoveUpButton != null) TaskMoveUpButton.IsEnabled = false;
                if (TaskMoveDownButton != null) TaskMoveDownButton.IsEnabled = false;

                if (StatusText != null)
                {
                    StatusText.Text = "جاهز - اختر عنصر للتعامل معه";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في مسح التحديد: {ex.Message}");
            }
        }

        private void TaskEditButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                // منع تعديل رسائل التيليجرام
                if (selectedItem.IsTelegramMessage)
                {
                    MessageBox.Show("❌ لا يمكن تعديل رسائل التيليجرام\n\n🤖 هذه الرسالة تم استلامها من بوت التيليجرام ولا يمكن تعديلها للحفاظ على سلامة البيانات.",
                        "تعديل غير مسموح", MessageBoxButton.OK, MessageBoxImage.Warning);
                    StatusText.Text = "⚠️ لا يمكن تعديل رسائل التيليجرام";
                    return;
                }

                EditContent(selectedItem);
            }
        }

        private void TaskCopyButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                CopyContent(selectedItem);
            }
        }

        private void TaskDeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                // منع حذف رسائل التيليجرام
                if (selectedItem.IsTelegramMessage)
                {
                    MessageBox.Show("❌ لا يمكن حذف رسائل التيليجرام\n\n🤖 هذه الرسالة تم استلامها من بوت التيليجرام ولا يمكن حذفها للحفاظ على سجل المراسلات.",
                        "حذف غير مسموح", MessageBoxButton.OK, MessageBoxImage.Warning);
                    StatusText.Text = "⚠️ لا يمكن حذف رسائل التيليجرام";
                    return;
                }

                DeleteContent(selectedItem);
            }
        }

        private void TaskFilesButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                ManageFiles(selectedItem);
            }
        }

        private void TaskAddButton_Click(object sender, RoutedEventArgs e)
        {
            var inputDialog = new InputDialog(currentSection);
            inputDialog.Owner = this;

            if (inputDialog.ShowDialog() == true)
            {
                var newItem = new ContentItem
                {
                    Section = currentSection,
                    Title = inputDialog.InputTitle,
                    Content = inputDialog.InputContent,
                    CreatedDate = DateTime.Now
                };

                contentItems.Add(newItem);
                SaveData(); // حفظ البيانات فوراً
                LoadCurrentSectionContent();
                StatusText.Text = $"✅ تم إضافة محتوى جديد في قسم: {currentSection}";
            }
        }

        private void TaskDividerButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dividerDialog = new DividerInputDialog();
                dividerDialog.Owner = this;

                if (dividerDialog.ShowDialog() == true)
                {
                    var newDivider = new ContentItem
                    {
                        Section = currentSection,
                        Title = "",
                        Content = "",
                        CreatedDate = DateTime.Now,
                        IsDivider = true,
                        DividerText = dividerDialog.DividerText ?? ""
                    };

                    contentItems.Add(newDivider);
                    SaveData(); // حفظ البيانات فوراً
                    LoadCurrentSectionContent();
                    StatusText.Text = $"✅ تم إضافة شريط تقسيم في قسم: {currentSection}";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة شريط التقسيم: {ex.Message}");
                StatusText.Text = "❌ حدث خطأ في إضافة شريط التقسيم";
            }
        }

        private void TaskSearchBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text == "🔍 البحث السريع...")
            {
                textBox.Text = "";
            }
        }

        private void TaskSearchBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "🔍 البحث السريع...";
            }
        }

        private void TaskSearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text != "🔍 البحث السريع..." && !string.IsNullOrWhiteSpace(textBox.Text))
            {
                SearchContent(textBox.Text);
            }
            else
            {
                // التمرير إلى الأعلى عند إلغاء البحث
                ContentScrollViewer.ScrollToTop();
                LoadCurrentSectionContent();
            }
        }

        private async void SearchContent(string searchTerm)
        {
            ContentPanel.Children.Clear();
            ClearSelection();

            // التمرير إلى الأعلى عند البحث
            ContentScrollViewer.ScrollToTop();

            // البحث التقليدي في جميع المحتويات بما في ذلك شرائط التقسيم
            var searchResults = contentItems.Where(item =>
                item.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                item.Content.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (item.IsDivider && item.DividerText.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))).ToList();

            // عرض النتائج التقليدية أولاً
            if (searchResults.Count > 0)
            {
                // عنوان النتائج التقليدية
                CreateAISearchHeader("🔍 النتائج المطابقة", searchResults.Count);

                // عرض النتائج مجمعة حسب القسم
                var groupedResults = searchResults.GroupBy(item => item.Section);

                foreach (var group in groupedResults)
                {
                    // عنوان القسم
                    CreateSectionHeader(group.Key, group.Count());

                    // عناصر القسم
                    foreach (var item in group)
                    {
                        CreateSearchResultItem(item, searchTerm);
                    }
                }
            }

            // إضافة البحث الذكي بالذكاء الاصطناعي
            await AddAISearchResults(searchTerm, searchResults);

            // تحديث شريط الحالة
            var totalResults = searchResults.Count;
            var sectionsCount = searchResults.GroupBy(item => item.Section).Count();
            StatusText.Text = $"🔍 البحث عن: {searchTerm} - {totalResults} نتيجة تقليدية في {sectionsCount} قسم + نتائج ذكية";
        }

        private async Task AddAISearchResults(string searchTerm, List<ContentItem> traditionalResults)
        {
            try
            {
                // إضافة مؤشر التحميل
                CreateAILoadingIndicator();

                // الحصول على رد الذكاء الاصطناعي
                var aiResponse = await GenerateAIResponse(searchTerm, contentItems);

                // إزالة مؤشر التحميل
                RemoveAILoadingIndicator();

                // عرض رد الذكاء الاصطناعي
                CreateAIResponseItem(aiResponse, searchTerm);

                // اقتراح محتوى ذي صلة
                var relatedContent = FindRelatedContent(searchTerm, traditionalResults);
                if (relatedContent.Any())
                {
                    CreateAISearchHeader("🤖 محتوى ذو صلة (مقترح بالذكاء الاصطناعي)", relatedContent.Count);
                    foreach (var item in relatedContent.Take(5)) // أقصى 5 اقتراحات
                    {
                        CreateSearchResultItem(item, searchTerm, true);
                    }
                }
            }
            catch (Exception ex)
            {
                RemoveAILoadingIndicator();
                CreateAIErrorItem($"❌ خطأ في البحث الذكي: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث الذكي: {ex.Message}");
            }
        }

        private List<ContentItem> FindRelatedContent(string searchTerm, List<ContentItem> excludeItems)
        {
            var excludeIds = excludeItems.Select(item => item.GetHashCode()).ToHashSet();
            var searchWords = searchTerm.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            return contentItems
                .Where(item => !excludeIds.Contains(item.GetHashCode()) && !item.IsDivider)
                .Where(item => searchWords.Any(word =>
                    item.Title.Contains(word, StringComparison.OrdinalIgnoreCase) ||
                    item.Content.Contains(word, StringComparison.OrdinalIgnoreCase)))
                .OrderByDescending(item => CalculateRelevanceScore(item, searchWords))
                .ToList();
        }

        private int CalculateRelevanceScore(ContentItem item, string[] searchWords)
        {
            int score = 0;
            foreach (var word in searchWords)
            {
                if (item.Title.Contains(word, StringComparison.OrdinalIgnoreCase))
                    score += 3; // العنوان له وزن أكبر
                if (item.Content.Contains(word, StringComparison.OrdinalIgnoreCase))
                    score += 1;
            }
            return score;
        }

        #region AI Search UI Elements

        private void CreateAISearchHeader(string title, int count)
        {
            var headerBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 15, 0, 10),
                Padding = new Thickness(15, 10, 15, 10)
            };

            var headerText = new TextBlock
            {
                Text = $"{title} ({count})",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            headerBorder.Child = headerText;
            ContentPanel.Children.Add(headerBorder);
        }

        private void CreateAIResponseItem(string response, string searchTerm)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0xE8, 0xF5, 0xE8)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(0x4C, 0xAF, 0x50)),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(20)
            };

            var stackPanel = new StackPanel();

            // عنوان الرد
            var titleBlock = new TextBlock
            {
                Text = "🤖 رد الذكاء الاصطناعي",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(0x2E, 0x7D, 0x32)),
                Margin = new Thickness(0, 0, 0, 10)
            };
            stackPanel.Children.Add(titleBlock);

            // محتوى الرد
            var responseBlock = new TextBlock
            {
                Text = response,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(0x1B, 0x5E, 0x20)),
                TextWrapping = TextWrapping.Wrap,
                LineHeight = 22
            };
            stackPanel.Children.Add(responseBlock);

            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void CreateAILoadingIndicator()
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0xFF, 0xF8, 0xE1)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(0xFF, 0xC1, 0x07)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(15, 10, 15, 10),
                Tag = "AI_LOADING" // للتعرف عليه لاحقاً
            };

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var loadingText = new TextBlock
            {
                Text = "🤖 جاري البحث بالذكاء الاصطناعي...",
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(0xE6, 0x8A, 0x00)),
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(loadingText);
            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void RemoveAILoadingIndicator()
        {
            var loadingElement = ContentPanel.Children
                .OfType<Border>()
                .FirstOrDefault(b => b.Tag?.ToString() == "AI_LOADING");

            if (loadingElement != null)
            {
                ContentPanel.Children.Remove(loadingElement);
            }
        }

        private void CreateAIErrorItem(string errorMessage)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0xFF, 0xEB, 0xEE)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(0xF4, 0x43, 0x36)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 15),
                Padding = new Thickness(15, 10, 15, 10)
            };

            var errorText = new TextBlock
            {
                Text = errorMessage,
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(0xC6, 0x28, 0x28)),
                TextWrapping = TextWrapping.Wrap
            };

            border.Child = errorText;
            ContentPanel.Children.Add(border);
        }

        #endregion

        private async void ShowNoSearchResults(string searchTerm)
        {
            var border = new Border
            {
                Background = Brushes.LightYellow,
                BorderBrush = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Padding = new Thickness(20)
            };

            var stackPanel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };

            var searchIcon = new TextBlock
            {
                Text = "🔍",
                FontSize = 48,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var noResultsTitle = new TextBlock
            {
                Text = $"لم يتم العثور على نتائج للبحث: {searchTerm}",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var suggestion = new TextBlock
            {
                Text = "جرب كلمات مفتاحية أخرى أو تأكد من الإملاء",
                FontSize = 14,
                Foreground = Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            stackPanel.Children.Add(searchIcon);
            stackPanel.Children.Add(noResultsTitle);
            stackPanel.Children.Add(suggestion);

            border.Child = stackPanel;
            ContentPanel.Children.Add(border);

            // إضافة البحث الذكي حتى لو لم توجد نتائج تقليدية
            await AddAISearchResults(searchTerm, new List<ContentItem>());

            StatusText.Text = $"🔍 البحث عن: {searchTerm} - لم يتم العثور على نتائج تقليدية + نتائج ذكية";
        }

        private void CreateSectionHeader(string sectionName, int count)
        {
            var headerBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 10, 0, 5),
                Padding = new Thickness(15, 8, 15, 8)
            };

            var headerText = new TextBlock
            {
                Text = $"📂 قسم {sectionName} ({count} نتيجة)",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White
            };

            headerBorder.Child = headerText;
            ContentPanel.Children.Add(headerBorder);
        }

        private void CreateSearchResultItem(ContentItem item, string searchTerm, bool isAISuggestion = false)
        {
            // تحديد الألوان حسب نوع العنصر
            Brush backgroundColor, borderColor;

            if (isAISuggestion)
            {
                backgroundColor = new SolidColorBrush(Color.FromRgb(0xE3, 0xF2, 0xFD));
                borderColor = new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3));
            }
            else if (item.IsDivider)
            {
                backgroundColor = Brushes.LightBlue;
                borderColor = Brushes.Blue;
            }
            else
            {
                backgroundColor = Brushes.LightYellow;
                borderColor = Brushes.Orange;
            }

            var border = new Border
            {
                Background = backgroundColor,
                BorderBrush = borderColor,
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(5),
                Margin = new Thickness(0, 0, 0, 10),
                Padding = new Thickness(15),
                Tag = item,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // إضافة حدث النقر للتحديد
            border.MouseLeftButtonUp += SelectContentItem;

            // إضافة حدث الضغط المزدوج للانتقال إلى مكان المحتوى
            border.MouseLeftButtonDown += (s, e) =>
            {
                if (e.ClickCount == 2)
                {
                    NavigateToContentInSection(item);
                }
            };

            var stackPanel = new StackPanel();

            // إضافة مؤشر للمحتوى المقترح بالذكاء الاصطناعي
            if (isAISuggestion)
            {
                var aiIndicator = new TextBlock
                {
                    Text = "🤖 مقترح بالذكاء الاصطناعي",
                    FontSize = 11,
                    FontStyle = FontStyles.Italic,
                    Foreground = new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3)),
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                stackPanel.Children.Add(aiIndicator);
            }

            // التعامل مع شرائط التقسيم
            if (item.IsDivider)
            {
                // عرض شريط التقسيم
                var dividerIcon = new TextBlock
                {
                    Text = "📏",
                    FontSize = 20,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                stackPanel.Children.Add(dividerIcon);

                var dividerLabel = new TextBlock
                {
                    Text = "شريط تقسيم",
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.Blue,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 5)
                };
                stackPanel.Children.Add(dividerLabel);

                if (!string.IsNullOrWhiteSpace(item.DividerText))
                {
                    var dividerTextBlock = new TextBlock
                    {
                        FontSize = 14,
                        FontWeight = FontWeights.Bold,
                        Foreground = Brushes.DarkBlue,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 10)
                    };
                    dividerTextBlock.Inlines.Add(HighlightSearchTerm(item.DividerText, searchTerm));
                    stackPanel.Children.Add(dividerTextBlock);
                }
            }
            else
            {
                // إضافة العنوان مع تمييز فقط إذا لم يكن فارغاً
                if (!string.IsNullOrWhiteSpace(item.Title))
                {
                    var titleBlock = new TextBlock
                    {
                        FontSize = 16,
                        FontWeight = FontWeights.Bold,
                        Foreground = new SolidColorBrush(Color.FromRgb(0x16, 0x16, 0x42)),
                        Margin = new Thickness(0, 0, 0, 10)
                    };
                    titleBlock.Inlines.Add(HighlightSearchTerm(item.Title, searchTerm));
                    stackPanel.Children.Add(titleBlock);
                }
            }

            // عرض الملف المرفق أو المحتوى مع تمييز (فقط للعناصر العادية، ليس شرائط التقسيم)
            if (!item.IsDivider)
            {
                if (item.AttachedFiles.Count > 0)
                {
                    // عرض الملف المرفق مع تمييز البحث
                    var attachedFile = item.AttachedFiles.First();
                    var fileDisplayElement = CreateSearchFileDisplayElement(attachedFile, item, searchTerm);
                    stackPanel.Children.Add(fileDisplayElement);
                }
                else
                {
                    // عرض المحتوى النصي مع تمييز
                    var contentBlock = new TextBlock
                    {
                        FontSize = 14,
                        Foreground = Brushes.DarkOrange,
                        TextWrapping = TextWrapping.Wrap,
                        Margin = new Thickness(0, 0, 0, 10)
                    };
                    contentBlock.Inlines.Add(HighlightSearchTerm(item.Content, searchTerm));
                    stackPanel.Children.Add(contentBlock);
                }
            }

            // معلومات إضافية
            var itemType = item.IsDivider ? "📏 شريط تقسيم" : "📄 محتوى";
            var infoBlock = new TextBlock
            {
                Text = $"📅 {item.CreatedDate:yyyy/MM/dd HH:mm} | 📂 {item.Section} | {itemType}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(0, 0, 0, 5)
            };

            stackPanel.Children.Add(infoBlock);

            border.Child = stackPanel;
            ContentPanel.Children.Add(border);
        }

        private void NavigateToContentInSection(ContentItem targetItem)
        {
            try
            {
                // تغيير القسم النشط إلى قسم المحتوى المطلوب
                currentSection = targetItem.Section;

                // تحديث ألوان أزرار الأقسام
                UpdateSectionButtonColors(GetSectionButton(targetItem.Section));

                // تحميل محتوى القسم
                LoadCurrentSectionContent();

                // البحث عن العنصر في القائمة المعروضة وتحديده
                this.Dispatcher.BeginInvoke(new Action(() =>
                {
                    foreach (Border border in ContentPanel.Children.OfType<Border>())
                    {
                        if (border.Tag is ContentItem item && item == targetItem)
                        {
                            // تحديد العنصر
                            SelectContentItem(border, null);

                            // التمرير إلى العنصر
                            border.BringIntoView();

                            // تمييز العنصر مؤقتاً
                            var originalBackground = border.Background;
                            border.Background = new SolidColorBrush(Color.FromRgb(0x4C, 0xAF, 0x50)); // أخضر فاتح

                            // إعادة اللون الأصلي بعد ثانيتين
                            var timer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(2) };
                            timer.Tick += (s, e) =>
                            {
                                border.Background = originalBackground;
                                timer.Stop();
                            };
                            timer.Start();

                            break;
                        }
                    }
                }), DispatcherPriority.Background);

                // تحديث شريط الحالة
                StatusText.Text = $"✅ تم الانتقال إلى المحتوى في قسم: {targetItem.Section}";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ خطأ في الانتقال إلى المحتوى: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"خطأ في NavigateToContentInSection: {ex.Message}");
            }
        }

        private Button GetSectionButton(string sectionName)
        {
            return sectionName switch
            {
                "توضيح" => BtnTawdeeh,
                "ردود" => BtnRodod,
                "عملاء" => BtnOmala,
                "فائزين" => BtnFaezeen,
                "مسابقات" => BtnMosabakat,
                "وكلاء" => BtnWokala,
                "السحب والايداع" => BtnSahbWaEeeda,
                "INZO IB" => BtnInzoIB,
                "النسخ" => BtnNasekh,
                "تعويض وتدقيق" => BtnTaaweedWaTadkeek,
                "المنشورات" => BtnManshoorat,
                "صور" => BtnSowar,
                "Meeting" => BtnMeeting,
                _ => BtnTawdeeh
            };
        }

        private FrameworkElement CreateSearchFileDisplayElement(string filePath, ContentItem item, string searchTerm)
        {
            var border = new Border
            {
                BorderBrush = Brushes.Orange,
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(3),
                Margin = new Thickness(0, 0, 0, 10),
                Background = Brushes.LightYellow,
                Padding = new Thickness(10)
            };

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            // أيقونة الملف
            var fileIcon = new TextBlock
            {
                Text = GetFileIcon(filePath),
                FontSize = 30,
                Margin = new Thickness(0, 0, 10, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var infoPanel = new StackPanel();

            // اسم الملف مع تمييز
            var fileName = new TextBlock
            {
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.DarkOrange,
                Margin = new Thickness(0, 0, 0, 5)
            };
            fileName.Inlines.Add(HighlightSearchTerm(Path.GetFileName(filePath), searchTerm));

            // معلومات الملف
            var fileInfo = new TextBlock
            {
                Text = $"📏 {GetFileSize(filePath)} | 📂 {Path.GetExtension(filePath).ToUpper()}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(0, 0, 0, 5)
            };

            // نص المحتوى مع تمييز إذا وجد
            if (!string.IsNullOrWhiteSpace(item.Content))
            {
                var contentText = new TextBlock
                {
                    FontSize = 12,
                    Foreground = Brushes.DarkOrange,
                    TextWrapping = TextWrapping.Wrap,
                    MaxWidth = 300
                };
                contentText.Inlines.Add(HighlightSearchTerm(item.Content, searchTerm));
                infoPanel.Children.Add(contentText);
            }

            infoPanel.Children.Add(fileName);
            infoPanel.Children.Add(fileInfo);

            stackPanel.Children.Add(fileIcon);
            stackPanel.Children.Add(infoPanel);

            border.Child = stackPanel;
            return border;
        }

        private System.Windows.Documents.Span HighlightSearchTerm(string text, string searchTerm)
        {
            var span = new System.Windows.Documents.Span();

            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(searchTerm))
            {
                span.Inlines.Add(text);
                return span;
            }

            var index = text.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase);
            if (index == -1)
            {
                span.Inlines.Add(text);
                return span;
            }

            // النص قبل المصطلح
            if (index > 0)
            {
                span.Inlines.Add(text.Substring(0, index));
            }

            // المصطلح المميز
            var highlightedRun = new System.Windows.Documents.Run(text.Substring(index, searchTerm.Length))
            {
                Background = Brushes.Yellow,
                FontWeight = FontWeights.Bold
            };
            span.Inlines.Add(highlightedRun);

            // النص بعد المصطلح
            if (index + searchTerm.Length < text.Length)
            {
                span.Inlines.Add(HighlightSearchTerm(text.Substring(index + searchTerm.Length), searchTerm));
            }

            return span;
        }

        private void AddContent(object sender, RoutedEventArgs e)
        {
            // فتح نافذة إدخال المحتوى الجديد
            var inputDialog = new InputDialog(currentSection);
            inputDialog.Owner = this;

            if (inputDialog.ShowDialog() == true)
            {
                // إنشاء عنصر محتوى جديد
                var newItem = new ContentItem
                {
                    Section = currentSection,
                    Title = inputDialog.InputTitle,
                    Content = inputDialog.InputContent,
                    CreatedDate = DateTime.Now,
                    Timestamp = DateTime.Now,
                    Id = Guid.NewGuid().ToString()
                };

                // إضافة العنصر إلى القائمة
                contentItems.Add(newItem);

                // حفظ البيانات (سيتم تفعيل المزامنة الشاملة تلقائياً)
                SaveData();

                // تحديث العرض
                LoadCurrentSectionContent();

                // تحديث شريط الحالة
                StatusText.Text = $"✅ تم إضافة محتوى جديد إلى قسم: {currentSection} وتم مزامنته";
            }
        }

        private void EditContent(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.Tag as ContentItem;
            EditContent(item);
        }

        private void EditContent(ContentItem item)
        {
            if (item != null)
            {
                // منع تعديل رسائل التيليجرام
                if (item.IsTelegramMessage)
                {
                    MessageBox.Show("❌ لا يمكن تعديل رسائل التيليجرام\n\n🤖 هذه الرسالة تم استلامها من بوت التيليجرام ولا يمكن تعديلها للحفاظ على سلامة البيانات.",
                        "تعديل غير مسموح", MessageBoxButton.OK, MessageBoxImage.Warning);
                    StatusText.Text = "⚠️ لا يمكن تعديل رسائل التيليجرام";
                    return;
                }

                // فتح نافذة تعديل المحتوى
                var inputDialog = new InputDialog(currentSection, item.Title, item.Content);
                inputDialog.Owner = this;

                if (inputDialog.ShowDialog() == true)
                {
                    // تحديث العنصر
                    item.Title = inputDialog.InputTitle;
                    item.Content = inputDialog.InputContent;
                    item.Timestamp = DateTime.Now; // تحديث الطابع الزمني

                    // حفظ البيانات فوراً (سيتم تفعيل المزامنة الشاملة تلقائياً)
                    SaveData();

                    // تحديث العرض
                    LoadCurrentSectionContent();
                    ClearSelection();

                    // تحديث شريط الحالة
                    StatusText.Text = $"✅ تم تعديل المحتوى في قسم: {currentSection} وتم مزامنته";
                }
            }
        }

        private void CopyContent(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.Tag as ContentItem;
            CopyContent(item);
        }

        private async void CopyContent(ContentItem item)
        {
            if (item != null)
            {
                try
                {
                    // إذا كان هناك ملف مرفق ونص محتوى، نسخ النص أولاً ثم الملف
                    if (item.AttachedFiles.Count > 0 && !string.IsNullOrWhiteSpace(item.Content))
                    {
                        var filePath = item.AttachedFiles.First();
                        if (File.Exists(filePath))
                        {
                            // نسخ النص أولاً
                            Clipboard.SetText(item.Content);

                            // عرض رسالة Toast للنص
                            ToastWindow.Show("تم نسخ النص");

                            // تحديث شريط الحالة
                            StatusText.Text = $"📋 تم نسخ النص: {item.Title}";

                            // انتظار فترة 0.25 ثانية (250 ميلي ثانية)
                            await Task.Delay(250);

                            // ثم نسخ الملف
                            var fileList = new System.Collections.Specialized.StringCollection();
                            fileList.Add(filePath);
                            Clipboard.SetFileDropList(fileList);

                            // عرض رسالة Toast للملف
                            ToastWindow.Show($"تم نسخ الملف: {Path.GetFileName(filePath)}");

                            // تحديث شريط الحالة
                            StatusText.Text = $"📁 تم نسخ النص والملف معاً";
                        }
                        else
                        {
                            // الملف غير موجود، نسخ النص فقط
                            Clipboard.SetText(item.Content);
                            ToastWindow.Show("الملف المرفق غير موجود - تم نسخ النص");
                            StatusText.Text = $"📋 تم نسخ المحتوى: {item.Title}";
                        }
                    }
                    // إذا كان هناك ملف مرفق فقط (بدون نص)
                    else if (item.AttachedFiles.Count > 0)
                    {
                        var filePath = item.AttachedFiles.First();
                        if (File.Exists(filePath))
                        {
                            var fileList = new System.Collections.Specialized.StringCollection();
                            fileList.Add(filePath);
                            Clipboard.SetFileDropList(fileList);

                            // عرض رسالة Toast مؤقتة
                            ToastWindow.Show($"تم نسخ الملف: {Path.GetFileName(filePath)}");

                            // تحديث شريط الحالة
                            StatusText.Text = $"📁 تم نسخ الملف المرفق: {Path.GetFileName(filePath)}";
                        }
                        else
                        {
                            ToastWindow.Show("الملف المرفق غير موجود");
                            StatusText.Text = "❌ الملف المرفق غير موجود";
                        }
                    }
                    // إذا كان هناك نص فقط (بدون ملف)
                    else
                    {
                        // نسخ نص المحتوى فقط
                        Clipboard.SetText(item.Content);

                        // عرض رسالة Toast مؤقتة
                        ToastWindow.Show("تم نسخ النص");

                        // تحديث شريط الحالة
                        StatusText.Text = $"📋 تم نسخ المحتوى: {item.Title}";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء النسخ:\n{ex.Message}",
                        "خطأ في النسخ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteContent(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.Tag as ContentItem;
            DeleteContent(item);
        }

        private void DeleteContent(ContentItem item)
        {
            if (item != null)
            {
                // منع حذف رسائل التيليجرام
                if (item.IsTelegramMessage)
                {
                    MessageBox.Show("❌ لا يمكن حذف رسائل التيليجرام\n\n🤖 هذه الرسالة تم استلامها من بوت التيليجرام ولا يمكن حذفها للحفاظ على سجل المراسلات.",
                        "حذف غير مسموح", MessageBoxButton.OK, MessageBoxImage.Warning);
                    StatusText.Text = "⚠️ لا يمكن حذف رسائل التيليجرام";
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من حذف '{item.Title}'؟",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف العنصر
                    contentItems.Remove(item);

                    // حفظ البيانات فوراً
                    SaveData();

                    // تحديث العرض
                    LoadCurrentSectionContent();
                    ClearSelection();

                    // تحديث شريط الحالة
                    StatusText.Text = $"✅ تم حذف المحتوى من قسم: {currentSection}";
                }
            }
        }

        private void ManageFiles(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.Tag as ContentItem;
            ManageFiles(item);
        }

        private void ManageFiles(ContentItem item)
        {
            if (item != null)
            {
                var fileWindow = new FileAttachmentWindow(item.Title, item.AttachedFiles);
                fileWindow.Owner = this;

                if (fileWindow.ShowDialog() == true)
                {
                    // نسخ الملفات إلى مجلد البرنامج وحفظ المسارات الجديدة
                    var newAttachedFiles = new List<string>();

                    foreach (var filePath in fileWindow.AttachedFiles)
                    {
                        var savedFilePath = SaveAttachedFile(filePath);
                        if (!string.IsNullOrEmpty(savedFilePath))
                        {
                            newAttachedFiles.Add(savedFilePath);
                        }
                    }

                    item.AttachedFiles = newAttachedFiles;
                    SaveData(); // حفظ البيانات فوراً
                    LoadCurrentSectionContent(); // تحديث العرض لإظهار عدد الملفات
                    ClearSelection();
                    StatusText.Text = $"📁 تم تحديث الملف المرفق: {item.Title} ({(item.AttachedFiles.Count > 0 ? "يوجد ملف" : "لا يوجد ملف")})";
                }
            }
        }

        private string SaveAttachedFile(string sourceFilePath)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                {
                    return sourceFilePath; // إذا كان الملف غير موجود، أعد المسار الأصلي
                }

                // إنشاء مجلد للملفات المرفقة
                var attachmentsFolder = Path.Combine(dataFolderPath, "Attachments");
                if (!Directory.Exists(attachmentsFolder))
                {
                    Directory.CreateDirectory(attachmentsFolder);
                }

                // الحصول على اسم الملف الأصلي
                var fileName = Path.GetFileName(sourceFilePath);

                // استخدام الاسم الأصلي للملف
                var destinationPath = Path.Combine(attachmentsFolder, fileName);

                // التحقق من وجود ملف بنفس الاسم وحذفه إذا وجد
                if (File.Exists(destinationPath))
                {
                    File.Delete(destinationPath);
                }

                // نسخ الملف
                File.Copy(sourceFilePath, destinationPath, true);

                System.Diagnostics.Debug.WriteLine($"تم نسخ الملف إلى: {destinationPath}");
                return destinationPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الملف المرفق: {ex.Message}");
                return sourceFilePath; // في حالة الخطأ، أعد المسار الأصلي
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void TaskMoveUpButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                MoveContentItem(selectedItem, -1);
            }
        }

        private void TaskMoveDownButton_Click(object sender, RoutedEventArgs e)
        {
            if (selectedItem != null)
            {
                MoveContentItem(selectedItem, 1);
            }
        }

        private void MoveContentItem(ContentItem item, int direction)
        {
            var sectionItems = contentItems.Where(x => x.Section == currentSection).ToList();
            var currentIndex = sectionItems.IndexOf(item);

            if (currentIndex == -1) return;

            var newIndex = currentIndex + direction;

            // التحقق من الحدود
            if (newIndex < 0 || newIndex >= sectionItems.Count) return;

            // العثور على المؤشرات في القائمة الأصلية
            var originalCurrentIndex = contentItems.IndexOf(item);
            var targetItem = sectionItems[newIndex];
            var originalTargetIndex = contentItems.IndexOf(targetItem);

            // تبديل العناصر
            contentItems[originalCurrentIndex] = targetItem;
            contentItems[originalTargetIndex] = item;

            // حفظ البيانات فوراً
            SaveData();

            // إعادة تحميل المحتوى
            LoadCurrentSectionContent();

            // إعادة تحديد العنصر
            selectedItem = item;

            StatusText.Text = $"✅ تم تحريك العنصر: {item.Title}";
        }
    }

    // فئة عنصر المحتوى
    public class ContentItem
    {
        public string Id { get; set; } = Guid.NewGuid().ToString(); // معرف فريد للعنصر
        public string Section { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now; // طابع زمني للمزامنة
        public List<string> AttachedFiles { get; set; } = new List<string>();
        public bool IsDivider { get; set; } = false;
        public string DividerText { get; set; } = "";
        public bool IsTelegramMessage { get; set; } = false; // خاصية لتمييز رسائل التيليجرام
        public string TelegramSender { get; set; } = ""; // اسم مرسل التيليجرام
        public int TelegramMessageId { get; set; } = 0; // معرف الرسالة في تيليجرام
        public long TelegramChatId { get; set; } = 0; // معرف المحادثة/القناة في تيليجرام
    }
}
