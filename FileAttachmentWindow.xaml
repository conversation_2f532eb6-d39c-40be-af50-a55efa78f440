<Window x:Class="InzoIB_Simple.FileAttachmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الملف المرفق" Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#161642" Padding="15,10" CornerRadius="5" Margin="0,0,0,20">
            <TextBlock x:Name="HeaderText" Text="إدارة الملف المرفق"
                       FontSize="18" FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- Content Display Area -->
        <Border Grid.Row="1" BorderBrush="#161642" BorderThickness="2" CornerRadius="5">
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                <Grid x:Name="ContentDisplayArea" Margin="10">
                    <!-- سيتم إضافة المحتوى هنا ديناميكياً -->
                </Grid>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal"
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="AddFileButton" Content="📎 إرفاق ملف"
                    Background="#161642" Foreground="White"
                    Padding="15,8" Margin="0,0,10,0"
                    FontSize="14" FontWeight="Bold"
                    Click="AddFileButton_Click"/>
            <Button x:Name="CopyAttachedFileButton" Content="📋 نسخ الملف المرفق"
                    Background="#4CAF50" Foreground="White"
                    Padding="15,8" Margin="10,0,10,0"
                    FontSize="14" FontWeight="Bold"
                    Click="CopyAttachedFileButton_Click"/>
            <Button x:Name="RemoveFileButton" Content="🗑️ إزالة الملف"
                    Background="#FF9800" Foreground="White"
                    Padding="15,8" Margin="10,0,10,0"
                    FontSize="14" FontWeight="Bold"
                    Click="RemoveFileButton_Click"/>
            <Button x:Name="CloseButton" Content="❌ إغلاق"
                    Background="#f44336" Foreground="White"
                    Padding="15,8" Margin="10,0,0,0"
                    FontSize="14" FontWeight="Bold"
                    Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
