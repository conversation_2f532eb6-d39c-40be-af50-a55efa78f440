<Window x:Class="InzoIB_Simple.BotTokenDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات بوت التيليجرام - Inzo IB"
        Height="550" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        MinHeight="550" MinWidth="600"
        Background="#1a1a2e"
        FontFamily="Segoe UI">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="🤖 إعدادات بوت التيليجرام"
                   FontSize="20" FontWeight="Bold" Foreground="White"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- المحتوى القابل للتمرير -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled" Margin="0,0,0,20">
            <StackPanel>
                <!-- الوصف -->
                <TextBlock Foreground="#ccc" FontSize="12" Margin="0,0,0,15"
                           TextWrapping="Wrap" LineHeight="18">
                    <Run Text="لاستخدام ميزة مزامنة رسائل التيليجرام، يرجى إدخال توكن البوت الخاص بك."/>
                    <LineBreak/>
                    <Run Text="يمكنك الحصول على التوكن من "/>
                    <Run Text="@BotFather" FontWeight="Bold" Foreground="#4CAF50"/>
                    <Run Text=" على التيليجرام."/>
                </TextBlock>

                <!-- تسمية حقل التوكن -->
                <TextBlock Text="🔑 توكن البوت:"
                           Foreground="White" FontSize="14" FontWeight="Bold"
                           Margin="0,0,0,5"/>

                <!-- حقل إدخال التوكن -->
                <TextBox x:Name="TokenTextBox"
                         Height="35" FontSize="12" Padding="10,8"
                         Background="#2d2d44" Foreground="White"
                         BorderBrush="#4CAF50" BorderThickness="2"
                         Margin="0,0,0,15"
                         TextWrapping="Wrap" AcceptsReturn="False"/>

                <!-- معلومات إضافية -->
                <Border Background="#2d2d44" CornerRadius="5" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📋 كيفية الحصول على التوكن:"
                                   Foreground="#4CAF50" FontWeight="Bold" FontSize="13" Margin="0,0,0,8"/>

                        <TextBlock Foreground="#ccc" FontSize="11" TextWrapping="Wrap" LineHeight="16">
                            <Run Text="1. ابحث عن "/>
                            <Run Text="@BotFather" FontWeight="Bold"/>
                            <Run Text=" في التيليجرام"/>
                            <LineBreak/>
                            <Run Text="2. أرسل الأمر "/>
                            <Run Text="/newbot" FontWeight="Bold"/>
                            <Run Text=" لإنشاء بوت جديد"/>
                            <LineBreak/>
                            <Run Text="3. اتبع التعليمات واختر اسم للبوت"/>
                            <LineBreak/>
                            <Run Text="4. انسخ التوكن الذي سيرسله لك BotFather"/>
                            <LineBreak/>
                            <Run Text="5. الصق التوكن في الحقل أعلاه"/>
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- معلومات إضافية عن الميزات -->
                <Border Background="#2d2d44" CornerRadius="5" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🚀 ميزات البوت المدعومة:"
                                   Foreground="#FF9800" FontWeight="Bold" FontSize="13" Margin="0,0,0,8"/>

                        <TextBlock Foreground="#ccc" FontSize="11" TextWrapping="Wrap" LineHeight="16">
                            <Run Text="✅ استقبال الرسائل من المحادثات الخاصة"/>
                            <LineBreak/>
                            <Run Text="✅ قراءة رسائل القنوات (عند جعل البوت مشرفاً)"/>
                            <LineBreak/>
                            <Run Text="✅ قراءة رسائل المجموعات (عند جعل البوت مشرفاً)"/>
                            <LineBreak/>
                            <Run Text="✅ مزامنة الرسائل بين أجهزة متعددة"/>
                            <LineBreak/>
                            <Run Text="✅ حفظ تلقائي لجميع الرسائل"/>
                            <LineBreak/>
                            <Run Text="✅ إمكانية حذف الرسائل"/>
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- نصائح الأمان -->
                <Border Background="#2d2d44" CornerRadius="5" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🔒 نصائح الأمان:"
                                   Foreground="#f44336" FontWeight="Bold" FontSize="13" Margin="0,0,0,8"/>

                        <TextBlock Foreground="#ccc" FontSize="11" TextWrapping="Wrap" LineHeight="16">
                            <Run Text="• لا تشارك توكن البوت مع أحد"/>
                            <LineBreak/>
                            <Run Text="• احتفظ بنسخة احتياطية من التوكن"/>
                            <LineBreak/>
                            <Run Text="• يمكن استخدام نفس البوت على أجهزة متعددة"/>
                            <LineBreak/>
                            <Run Text="• التوكن يحفظ مشفراً في البرنامج"/>
                            <LineBreak/>
                            <Run Text="• يمكن تغيير التوكن في أي وقت"/>
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- معلومات Chat ID -->
                <Border Background="#2d2d44" CornerRadius="5" Padding="15" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="🆔 معلومات Chat ID:"
                                   Foreground="#2196F3" FontWeight="Bold" FontSize="13" Margin="0,0,0,8"/>

                        <TextBlock Foreground="#ccc" FontSize="11" TextWrapping="Wrap" LineHeight="16">
                            <Run Text="• سيظهر Chat ID في رسائل التأكيد"/>
                            <LineBreak/>
                            <Run Text="• يمكن استخدام Chat ID لإرسال رسائل مباشرة"/>
                            <LineBreak/>
                            <Run Text="• كل محادثة/قناة/مجموعة لها Chat ID فريد"/>
                            <LineBreak/>
                            <Run Text="• Chat ID يبدأ بـ - للمجموعات والقنوات"/>
                        </TextBlock>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button x:Name="SaveButton" Content="💾 حفظ" 
                    Width="100" Height="35" Margin="0,0,10,0"
                    Background="#4CAF50" Foreground="White" FontWeight="Bold"
                    BorderThickness="0" Click="SaveButton_Click"/>
            
            <Button x:Name="TestButton" Content="🧪 اختبار" 
                    Width="100" Height="35" Margin="0,0,10,0"
                    Background="#FF9800" Foreground="White" FontWeight="Bold"
                    BorderThickness="0" Click="TestButton_Click"/>
            
            <Button x:Name="CancelButton" Content="❌ إلغاء" 
                    Width="100" Height="35"
                    Background="#f44336" Foreground="White" FontWeight="Bold"
                    BorderThickness="0" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
