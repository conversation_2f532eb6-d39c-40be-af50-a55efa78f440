<Window x:Class="InzoIB_Simple.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Inzo IB v7.4" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        WindowState="Normal"
        ShowInTaskbar="True"
        Visibility="Visible">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>



        <!-- Navigation Panel -->
        <Border Grid.Row="0" Grid.Column="0" Background="#f5f5f5" BorderBrush="#ddd" BorderThickness="0,0,1,0">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="10">
                    <!-- Navigation Buttons -->
                    <Button x:Name="BtnTawdeeh" Content="📝 توضيح" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnRodod" Content="💬 ردود" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnOmala" Content="👥 عملاء" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnFaezeen" Content="🏆 فائزين" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnMosabakat" Content="🎯 مسابقات" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnWokala" Content="🤝 وكلاء" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnSahbWaEeeda" Content="💰 السحب والايداع" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnInzoIB" Content="🏢 INZO IB" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnNasekh" Content="📄 النسخ" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnTaaweedWaTadkeek" Content="✅ تعويض وتدقيق" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnManshoorat" Content="📢 المنشورات" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnSowar" Content="🖼️ صور" 
                            Background="#161642" Foreground="White" 
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                    <Button x:Name="BtnMeeting" Content="🎥 Meeting"
                            Background="#161642" Foreground="White"
                            Margin="5" Padding="15,10" FontSize="14" FontWeight="Bold"
                            Click="NavigateToSection"/>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Row="0" Grid.Column="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Content Display -->
            <ScrollViewer x:Name="ContentScrollViewer" Grid.Row="0" VerticalScrollBarVisibility="Auto">
                <StackPanel x:Name="ContentPanel">
                    <!-- Welcome Content -->
                    <Border Background="White" BorderBrush="#ddd" BorderThickness="1" CornerRadius="5" Margin="0,0,0,10" Padding="15">
                        <StackPanel>
                            <TextBlock Text="🎉 مرحباً بك في Inzo IB v7.4" FontSize="18" FontWeight="Bold" Foreground="#161642" Margin="0,0,0,10"/>
                            <TextBlock Text="نظام إدارة المحتوى المتطور مع واجهة عربية كاملة" FontSize="14" Foreground="#666" TextWrapping="Wrap" Margin="0,0,0,10"/>
                            <TextBlock Text="📋 الميزات المتوفرة:" FontSize="14" FontWeight="Bold" Foreground="#161642" Margin="0,0,0,5"/>
                            <TextBlock Text="• 13 قسم للمحتوى مع أيقونات" FontSize="12" Foreground="#666" Margin="0,0,0,3"/>
                            <TextBlock Text="• بحث فوري عبر جميع المحتوى" FontSize="12" Foreground="#666" Margin="0,0,0,3"/>
                            <TextBlock Text="• إضافة وتعديل وحذف المحتوى" FontSize="12" Foreground="#666" Margin="0,0,0,3"/>
                            <TextBlock Text="• واجهة عربية مع دعم RTL" FontSize="12" Foreground="#666" Margin="0,0,0,10"/>
                            <StackPanel Orientation="Horizontal">
                                <Button Content="✏️ تعديل" Background="#161642" Foreground="White" Padding="8,5" Margin="2" FontSize="12"/>
                                <Button Content="🗑️ حذف" Background="#f44336" Foreground="White" Padding="8,5" Margin="2" FontSize="12"/>
                                <Button Content="📁 ملفات" Background="#161642" Foreground="White" Padding="8,5" Margin="2" FontSize="12"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Grid>

        <!-- Task Bar -->
        <Border Grid.Row="1" Grid.ColumnSpan="2" Background="#161642" BorderBrush="#ddd" BorderThickness="0,1,0,0" Padding="6,3" Panel.ZIndex="999">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto" MinWidth="600"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBox x:Name="TaskSearchBox" Text="🔍 البحث السريع..."
                             FontSize="12" Padding="8,4" Margin="5,0" Width="600" Height="28"
                             BorderBrush="White" BorderThickness="1"
                             Background="White" Foreground="Black"
                             AcceptsReturn="True" AcceptsTab="True"
                             TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                             VerticalAlignment="Center" MaxHeight="28"
                             GotFocus="TaskSearchBox_GotFocus"
                             LostFocus="TaskSearchBox_LostFocus"
                             TextChanged="TaskSearchBox_TextChanged"/>
                </StackPanel>

                <!-- Copy Button in Middle Column -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Left">
                    <Button x:Name="TaskCopyButton" Content="📋 نسخ"
                            Background="#4CAF50" Foreground="White"
                            Padding="8,4" Margin="25,0,2,0" FontSize="11" FontWeight="Bold"
                            Click="TaskCopyButton_Click" IsEnabled="True"
                            Panel.ZIndex="1000" Visibility="Visible"/>
                </StackPanel>

                <!-- All Buttons on Right -->
                <DockPanel Grid.Column="2" HorizontalAlignment="Stretch" VerticalAlignment="Center" Margin="0,0,10,0">
                    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled"
                                  DockPanel.Dock="Right" HorizontalAlignment="Right">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,0">
                    <!-- Action Buttons -->
                    <Button x:Name="TaskEditButton" Content="✏️ تعديل"
                            Background="#161642" Foreground="White"
                            Padding="6,2" Margin="2,0" FontSize="10" FontWeight="Bold"
                            Click="TaskEditButton_Click" IsEnabled="True"
                            Panel.ZIndex="1000" Visibility="Visible"/>
                    <Button x:Name="TaskDeleteButton" Content="🗑️ حذف"
                            Background="#f44336" Foreground="White"
                            Padding="6,2" Margin="2,0" FontSize="10" FontWeight="Bold"
                            Click="TaskDeleteButton_Click" IsEnabled="True"
                            Panel.ZIndex="1000" Visibility="Visible"/>
                    <Button x:Name="TaskFilesButton" Content="📁 ملفات"
                            Background="#FF9800" Foreground="White"
                            Padding="6,2" Margin="2,0" FontSize="10" FontWeight="Bold"
                            Click="TaskFilesButton_Click" IsEnabled="True"
                            Panel.ZIndex="1000" Visibility="Visible"/>

                    <!-- Move Buttons -->
                    <Button x:Name="TaskMoveUpButton" Content="⬆️ أعلى"
                            Background="#FF9800" Foreground="White"
                            Padding="6,2" Margin="8,0,2,0" FontSize="10" FontWeight="Bold"
                            Click="TaskMoveUpButton_Click" IsEnabled="True"
                            Panel.ZIndex="1000" Visibility="Visible"/>
                    <Button x:Name="TaskMoveDownButton" Content="⬇️ أسفل"
                            Background="#FF9800" Foreground="White"
                            Padding="6,2" Margin="2,0" FontSize="10" FontWeight="Bold"
                            Click="TaskMoveDownButton_Click" IsEnabled="True"
                            Panel.ZIndex="1000" Visibility="Visible"/>

                    <!-- Add Buttons -->
                    <Button x:Name="TaskDividerButton" Content="📏 شريط تقسيم"
                            Background="#9C27B0" Foreground="White"
                            Padding="6,2" Margin="8,0,2,0" FontSize="10" FontWeight="Bold"
                            Click="TaskDividerButton_Click"
                            Panel.ZIndex="1000" Visibility="Visible"/>
                    <Button x:Name="TaskAddButton" Content="➕ إضافة جديد"
                            Background="#2196F3" Foreground="White"
                            Padding="6,2" Margin="2,0,2,0" FontSize="10" FontWeight="Bold"
                            Click="TaskAddButton_Click"
                            Panel.ZIndex="1000" Visibility="Visible"/>

                    <!-- Settings Menu Button -->
                    <Button x:Name="SettingsMenuButton" Content="⚙️ إعدادات"
                            Background="#607D8B" Foreground="White"
                            Padding="6,2" Margin="2,0,10,0" FontSize="10" FontWeight="Bold"
                            Click="SettingsMenuButton_Click"
                            Panel.ZIndex="1000" Visibility="Visible"/>

                        </StackPanel>
                    </ScrollViewer>
                </DockPanel>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="2" Grid.ColumnSpan="2" Background="#161642" Padding="15,8">
            <StackPanel Orientation="Horizontal">
                <TextBlock x:Name="StatusText" Text="✅ تم تشغيل البرنامج بنجاح" Foreground="White" FontSize="12"/>
                <TextBlock Text=" | " Foreground="#ccc" FontSize="12" Margin="10,0"/>
                <TextBlock x:Name="TimeText" Foreground="#ccc" FontSize="12"/>
            </StackPanel>
        </Border>

        <!-- Settings Popup Menu -->
        <Popup x:Name="SettingsPopup"
               PlacementTarget="{Binding ElementName=SettingsMenuButton}"
               Placement="Bottom"
               StaysOpen="False"
               AllowsTransparency="True"
               PopupAnimation="Slide">
            <Border Background="#2d2d44"
                    BorderBrush="#4CAF50"
                    BorderThickness="1"
                    CornerRadius="5"
                    Margin="5">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" Opacity="0.3"/>
                </Border.Effect>
                <StackPanel Margin="5">
                    <!-- Bot Settings Button -->
                    <Button x:Name="BotSettingsButton" Content="🤖 إعدادات البوت"
                            Background="Transparent" Foreground="White"
                            Padding="10,5" FontSize="11" FontWeight="Bold"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            Click="BotSettingsButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#4CAF50"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- API Key Button -->
                    <Button x:Name="ApiKeyButton" Content="🔑 API Key"
                            Background="Transparent" Foreground="White"
                            Padding="10,5" FontSize="11" FontWeight="Bold"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            Click="ApiKeyButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FF9800"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- External Network Button -->
                    <Button x:Name="ExternalNetworkButton" Content="🌐 الشبكات الخارجية"
                            Background="Transparent" Foreground="White"
                            Padding="10,5" FontSize="11" FontWeight="Bold"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            Click="ExternalNetworkButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#9C27B0"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- IP Info Button -->
                    <Button x:Name="IPInfoButton" Content="📍 معلومات IP"
                            Background="Transparent" Foreground="White"
                            Padding="10,5" FontSize="11" FontWeight="Bold"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            Click="IPInfoButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#2196F3"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Firestore Settings Button -->
                    <Button x:Name="FirestoreSettingsButton" Content="🔥 إعدادات Firestore"
                            Background="Transparent" Foreground="White"
                            Padding="10,5" FontSize="11" FontWeight="Bold"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            Click="FirestoreSettingsButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FF5722"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Sync Status Button -->
                    <Button x:Name="SyncStatusButton" Content="🌍 حالة المزامنة"
                            Background="Transparent" Foreground="White"
                            Padding="10,5" FontSize="11" FontWeight="Bold"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            Click="SyncStatusButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#4CAF50"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- Test Sync Button -->
                    <Button x:Name="TestSyncButton" Content="🧪 اختبار المزامنة"
                            Background="Transparent" Foreground="White"
                            Padding="10,5" FontSize="11" FontWeight="Bold"
                            BorderThickness="0"
                            HorizontalAlignment="Stretch"
                            Click="TestSyncButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FF9800"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Border>
        </Popup>
    </Grid>
</Window>
