🚀 تعليمات سريعة - الشبكات الخارجية
===================================

✅ تم إضافة دعم الربط عبر شبكات مختلفة للبرنامج!

🎯 ما الجديد؟
• زر جديد: "🌐 الشبكات الخارجية" في قائمة الإعدادات
• إمكانية إضافة عناوين IP خارجية للمزامنة
• اختبار الاتصال قبل الإضافة
• حفظ الإعدادات تلقائياً

📋 كيفية الاستخدام:

**1️⃣ فتح الإعدادات:**
• انقر "⚙️ إعدادات"
• اختر "🌐 الشبكات الخارجية"

**2️⃣ إضافة شبكة خارجية:**
• أدخل عنوان IP: مثل ************
• أو اسم المضيف: مثل myserver.com
• أضف وصف: مثل "جهاز المكتب"
• انقر "🔍 اختبار" للتحقق
• انقر "➕ إضافة العنوان"

**3️⃣ حفظ وتطبيق:**
• انقر "💾 حفظ وإغلاق"
• البرنامج سيعيد تشغيل المزامنة
• البحث في الشبكات الخارجية يبدأ تلقائياً

🧪 اختبار سريع:

**السيناريو:**
• جهاز في المنزل (IP: *************)
• جهاز في المكتب (IP خارجي: ************)

**الخطوات:**
1. في جهاز المنزل: أضف ************ للشبكات الخارجية
2. في جهاز المكتب: أضف IP المنزل للشبكات الخارجية
3. تأكد من فتح المنفذ 8080 في كلا الراوترين
4. اختبر المزامنة بإضافة محتوى

📊 مؤشرات الحالة:

**في نافذة الإعدادات:**
• "✅ متصل" - الجهاز متاح
• "❌ غير متصل" - الجهاز غير متاح
• "🔄 جاري الاختبار..." - فحص الاتصال

**في البرنامج الرئيسي:**
• "🌐 تم تحديث إعدادات الشبكة - X شبكة خارجية"
• "🔗 متصل مع X جهاز" - العدد الإجمالي

🔧 متطلبات الشبكة الخارجية:

**على الجهاز المستهدف:**
• تشغيل نفس البرنامج
• فتح المنفذ 8080
• عنوان IP ثابت أو Dynamic DNS

**على الراوتر:**
• Port Forwarding للمنفذ 8080
• أو تفعيل UPnP

💡 نصائح سريعة:

• **اختبر محلياً أولاً** - تأكد من عمل المزامنة محلياً
• **استخدم عناوين ثابتة** - لتجنب تغيير العناوين
• **فعل جدار الحماية** - للحماية من الاتصالات غير المرغوبة
• **راقب الاتصالات** - تحقق من سجلات Debug

🔍 حل المشاكل السريع:

❌ **"فشل الاتصال"**
   → تحقق من تشغيل البرنامج على الجهاز المستهدف
   → تأكد من فتح المنافذ في الراوتر

❌ **"عنوان غير صحيح"**
   → استخدم تنسيق: ************* أو example.com
   → تجنب المسافات والرموز الخاصة

❌ **"لا يحفظ الإعدادات"**
   → جرب تشغيل البرنامج كمدير
   → تحقق من مساحة القرص

🎯 أمثلة سريعة:

**للمنزل والمكتب:**
```
العنوان: office.mycompany.com
الوصف: جهاز المكتب
```

**لعدة فروع:**
```
العنوان: ************
الوصف: فرع القاهرة

العنوان: 203.0.113.20  
الوصف: فرع الإسكندرية
```

⚠️ تذكير مهم:

• هذه الميزة للشبكات الموثوقة فقط
• لا تضع عناوين غير آمنة
• استخدم VPN للحماية الإضافية إذا أمكن

🎉 النتيجة:

✅ **ربط عبر الإنترنت** - مزامنة من أي مكان
✅ **إعداد سهل** - واجهة بسيطة وواضحة
✅ **اختبار مدمج** - التحقق من الاتصال
✅ **حفظ تلقائي** - لا فقدان للإعدادات

🎯 الآن يمكن ربط البرنامج عبر شبكات مختلفة بسهولة!
