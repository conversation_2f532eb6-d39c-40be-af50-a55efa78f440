🌐 دليل المزامنة عبر الشبكة - Inzo IB v7.4
=============================================

📋 نظرة عامة:
تم تطوير نظام مزامنة متقدم يسمح بمشاركة رسائل بوت التيليجرام بين جميع الأجهزة المتصلة بنفس الشبكة المحلية.

🎯 الميزات الجديدة:
• مزامنة فورية لرسائل التيليجرام بين جميع الأجهزة
• اكتشاف تلقائي للأجهزة في الشبكة المحلية
• واجهة مستخدم محدثة مع مؤشرات الحالة
• خادم HTTP مدمج للمزامنة
• حماية من التكرار للرسائل المزامنة

🔧 كيفية العمل:
1. عند تشغيل البرنامج، يبدأ خادم HTTP على المنفذ 8080
2. يبحث البرنامج تلقائياً عن أجهزة أخرى تشغل نفس البرنامج
3. عند وصول رسالة جديدة من التيليجرام، يتم:
   - إضافتها إلى البرنامج المحلي
   - إرسالها إلى جميع الأجهزة المتصلة
   - عرضها على جميع الأجهزة فوراً

📱 واجهة المستخدم:
• زر "🌐 الأجهزة (0)": يعرض عدد الأجهزة المتصلة
• النقر على الزر يعرض تفاصيل الأجهزة المتصلة
• شريط الحالة يعرض معلومات الاتصال

🚀 التشغيل:
1. تأكد من تشغيل البرنامج على جميع الأجهزة المطلوبة
2. تأكد من اتصال جميع الأجهزة بنفس الشبكة المحلية
3. انتظر بضع ثوانٍ حتى يتم اكتشاف الأجهزة
4. ستظهر الرسائل الجديدة على جميع الأجهزة تلقائياً

🔒 الأمان:
• يعمل النظام فقط على الشبكة المحلية
• لا يتم إرسال البيانات عبر الإنترنت
• التشفير المحلي للبيانات الحساسة

⚠️ متطلبات النظام:
• Windows 10 أو أحدث
• .NET 6.0 Runtime
• اتصال بالشبكة المحلية
• عدم حجب المنفذ 8080 بواسطة جدار الحماية

🛠️ استكشاف الأخطاء:
إذا لم تظهر الأجهزة:
1. تحقق من جدار الحماية
2. تأكد من الاتصال بنفس الشبكة
3. أعد تشغيل البرنامج
4. تحقق من عدم استخدام المنفذ 8080 من برنامج آخر

📞 الدعم الفني:
للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

تاريخ التحديث: 2025-01-19
إصدار البرنامج: Inzo IB v7.4 مع المزامنة عبر الشبكة
