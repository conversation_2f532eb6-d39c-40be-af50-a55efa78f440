{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"InzoIB_v7.4_Simple/1.0.0": {"dependencies": {"Google.Apis.Auth": "1.68.0", "Google.Cloud.Firestore": "3.8.0", "Newtonsoft.Json": "13.0.3", "OpenAI": "2.0.0", "Telegram.Bot": "19.0.0"}, "runtime": {"InzoIB_v7.4_Simple.dll": {}}}, "Google.Api.CommonProtos/2.15.0": {"dependencies": {"Google.Protobuf": "3.25.0"}, "runtime": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"assemblyVersion": "2.15.0.0", "fileVersion": "2.15.0.0"}}}, "Google.Api.Gax/4.8.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Api.Gax.Grpc/4.8.0": {"dependencies": {"Google.Api.CommonProtos": "2.15.0", "Google.Api.Gax": "4.8.0", "Google.Apis.Auth": "1.68.0", "Grpc.Auth": "2.60.0", "Grpc.Core.Api": "2.60.0", "Grpc.Net.Client": "2.60.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Grpc.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Apis/1.68.0": {"dependencies": {"Google.Apis.Core": "1.68.0"}, "runtime": {"lib/net6.0/Google.Apis.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.Auth/1.68.0": {"dependencies": {"Google.Apis": "1.68.0", "Google.Apis.Core": "1.68.0", "System.Management": "7.0.2"}, "runtime": {"lib/net6.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Apis.Core/1.68.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Google.Apis.Core.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}}, "Google.Cloud.Firestore/3.8.0": {"dependencies": {"Google.Cloud.Firestore.V1": "3.8.0", "System.Collections.Immutable": "6.0.0", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Firestore.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.8.0.0"}}}, "Google.Cloud.Firestore.V1/3.8.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0", "Google.Cloud.Location": "2.3.0", "Google.LongRunning": "3.3.0"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Firestore.V1.dll": {"assemblyVersion": "3.8.0.0", "fileVersion": "3.8.0.0"}}}, "Google.Cloud.Location/2.3.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Location.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.0"}}}, "Google.LongRunning/3.3.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0"}, "runtime": {"lib/netstandard2.0/Google.LongRunning.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "Google.Protobuf/3.25.0": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.25.0.0", "fileVersion": "3.25.0.0"}}}, "Grpc.Auth/2.60.0": {"dependencies": {"Google.Apis.Auth": "1.68.0", "Grpc.Core.Api": "2.60.0"}, "runtime": {"lib/netstandard2.0/Grpc.Auth.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Core.Api/2.60.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.Client/2.60.0": {"dependencies": {"Grpc.Net.Common": "2.60.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Grpc.Net.Client.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.Common/2.60.0": {"dependencies": {"Grpc.Core.Api": "2.60.0"}, "runtime": {"lib/net6.0/Grpc.Net.Common.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "OpenAI/2.0.0": {"dependencies": {"System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/OpenAI.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "6.0.9"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.46703"}}}, "System.CodeDom/7.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1523.11507"}}}, "System.Linq.Async/6.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1.35981"}}}, "System.Management/7.0.2": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"lib/net6.0/System.Management.dll": {"assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.9"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.9": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2523.51912"}}}, "Telegram.Bot/19.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Telegram.Bot.dll": {"assemblyVersion": "19.0.0.0", "fileVersion": "19.0.0.0"}}}}}, "libraries": {"InzoIB_v7.4_Simple/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Google.Api.CommonProtos/2.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-k+43CTEgMbT4C2JjcIJon3GvMNFJ3kLMjPTedOkXUAt0D5cFfrlee+M4oieueAVhbXktR89yoj5O/kVmC/AKJw==", "path": "google.api.commonprotos/2.15.0", "hashPath": "google.api.commonprotos.2.15.0.nupkg.sha512"}, "Google.Api.Gax/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlV8Jq/G5CQAA3PwYAuKGjfzGOP7AvjhREnE6vgZlzxREGYchHudZWa2PWSqFJL+MBtz9YgitLpRogANN3CVvg==", "path": "google.api.gax/4.8.0", "hashPath": "google.api.gax.4.8.0.nupkg.sha512"}, "Google.Api.Gax.Grpc/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-njc<PERSON>rFMZTmG2xdar6o5zyj7YVCUv6JNWU7un+q15Fm9DpGt0HSqHVnabVD2LBjUa3JWrqWX2kVv0w7qx4+tFQ==", "path": "google.api.gax.grpc/4.8.0", "hashPath": "google.api.gax.grpc.4.8.0.nupkg.sha512"}, "Google.Apis/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-s2MymhdpH+ybZNBeZ2J5uFgFHApBp+QXf9FjZSdM1lk/vx5VqIknJwnaWiuAzXxPrLEkesX0Q+UsiWn39yZ9zw==", "path": "google.apis/1.68.0", "hashPath": "google.apis.1.68.0.nupkg.sha512"}, "Google.Apis.Auth/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-hFx8Qz5bZ4w0hpnn4tSmZaaFpjAMsgVElZ+ZgVLUZ2r9i+AKcoVgwiNfv1pruNS5cCvpXqhKECbruBCfRezPHA==", "path": "google.apis.auth/1.68.0", "hashPath": "google.apis.auth.1.68.0.nupkg.sha512"}, "Google.Apis.Core/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-pAqwa6pfu53UXCR2b7A/PAPXeuVg6L1OFw38WckN27NU2+mf+KTjoEg2YGv/f0UyKxzz7DxF1urOTKg/6dTP9g==", "path": "google.apis.core/1.68.0", "hashPath": "google.apis.core.1.68.0.nupkg.sha512"}, "Google.Cloud.Firestore/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-22Isdmp48+XzOOI3pmmd3dLgJT5JKCUIw0eMqoQfZL96spLc3SlNTeC7WerAsDhvii73S0VLsLXb5KfEmcTA/Q==", "path": "google.cloud.firestore/3.8.0", "hashPath": "google.cloud.firestore.3.8.0.nupkg.sha512"}, "Google.Cloud.Firestore.V1/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-wBO8OtxkP7y4CnKjUMJo2YyKoGWbN6UkmWN+Rm9p2vqvUwgHPIT5OZVlJBxPyFDWLUuGSBYVBoeathsZfjt/gg==", "path": "google.cloud.firestore.v1/3.8.0", "hashPath": "google.cloud.firestore.v1.3.8.0.nupkg.sha512"}, "Google.Cloud.Location/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ABQ4EM7FsOM7tx0cmlkZmHFqH1LeCf4teWPM26UT7mZJzlH4Pk8HUcyi/xEFe3l6LanNFCTHbKT+eOlQ/axkJg==", "path": "google.cloud.location/2.3.0", "hashPath": "google.cloud.location.2.3.0.nupkg.sha512"}, "Google.LongRunning/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-F2SZ83Jo466Wj/s1Z7QhIAmWBXxJZQyXZpcx0P8BR7d6s0FAj67vQjeUPESSJcvsy8AqYiYBhkUr2YpZhTQeHg==", "path": "google.longrunning/3.3.0", "hashPath": "google.longrunning.3.3.0.nupkg.sha512"}, "Google.Protobuf/3.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-pIEkH1IqZV1iK8J5MYdG1kOyY0EoQLB6yEKvBq12RYNtvGXwCvnQg5zQsFmcqAEPtIZvSqPozIbUZaEd5a2gCg==", "path": "google.protobuf/3.25.0", "hashPath": "google.protobuf.3.25.0.nupkg.sha512"}, "Grpc.Auth/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-mJAllsSsFix/wrp1l5QmHXRlzMFmnIGZJYuHG1thkDJPUE9ltHPkdL5x30xGpqKvka02Il2oHd66gt8YfSOIWg==", "path": "grpc.auth/2.60.0", "hashPath": "grpc.auth.2.60.0.nupkg.sha512"}, "Grpc.Core.Api/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-VWah+8dGJhhsay5BQ/Ljq6GYDWj0lSjdzqyoBgUQhXTbBqhs+q5dRFROKxI1xxzlL4pfUO45cf/y+KnHVFG9ew==", "path": "grpc.core.api/2.60.0", "hashPath": "grpc.core.api.2.60.0.nupkg.sha512"}, "Grpc.Net.Client/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-J9U96gjZHOcqSgAThg9vZZhLsbTD005bUggPtMP/RVQnGc3+tQJTpkRUCJtJWq9cykNydsRVoyU38TjPP/VJ4A==", "path": "grpc.net.client/2.60.0", "hashPath": "grpc.net.client.2.60.0.nupkg.sha512"}, "Grpc.Net.Common/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/917aplgD1RA0q1cd9WpnMGyl9Luu3WZl6ZMpPvNQwg2TNw/3uXUDSriDBybeCtxnKUCtxUcWO3WsVkhM1DcA==", "path": "grpc.net.common/2.60.0", "hashPath": "grpc.net.common.2.60.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OpenAI/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WI9e5tC15ZBt02scCp2gcHrjxQRVX2Ws3eUu/YsICqellH3MwdRggGujSZg69oBJMtk1AYmqb9l08ix0l5AcqQ==", "path": "openai/2.0.0", "hashPath": "openai.2.0.0.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Linq.Async/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "path": "system.linq.async/6.0.1", "hashPath": "system.linq.async.6.0.1.nupkg.sha512"}, "System.Management/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "path": "system.management/7.0.2", "hashPath": "system.management.7.0.2.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-2j16oUgtIzl7Xtk7demG0i/v5aU/ZvULcAnJvPb63U3ZhXJ494UYcxuEj5Fs49i3XDrk5kU/8I+6l9zRCw3cJw==", "path": "system.text.json/6.0.9", "hashPath": "system.text.json.6.0.9.nupkg.sha512"}, "Telegram.Bot/19.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Q16IOitgjGoaJOuqgKQy0FeF+hr/ncmlX2esrhCC7aiyhSX7roYEriWaGAHkQZR8QzbImjFfl4eQh2IxcnOrPg==", "path": "telegram.bot/19.0.0", "hashPath": "telegram.bot.19.0.0.nupkg.sha512"}}}