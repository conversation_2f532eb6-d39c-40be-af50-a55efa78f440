﻿#pragma checksum "..\..\..\IPInfoDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E9716FA8EA68B806FCA4DA0BD6A07C35C9AB28DE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InzoIB_Simple {
    
    
    /// <summary>
    /// IPInfoDialog
    /// </summary>
    public partial class IPInfoDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 59 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MachineNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyMachineNameButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UserNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OSVersionTextBox;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocalIPTextBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyLocalIPButton;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExternalIPTextBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyExternalIPButton;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SubnetTextBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ServerStatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PortTextBlock;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectedDevicesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox DevicesListBox;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExternalNetworksCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ExternalNetworksListBox;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\IPInfoDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InzoIB_v7.4_Simple;V7.4.0.0;component/ipinfodialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\IPInfoDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MachineNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.CopyMachineNameButton = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\IPInfoDialog.xaml"
            this.CopyMachineNameButton.Click += new System.Windows.RoutedEventHandler(this.CopyMachineName_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.UserNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.OSVersionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.LocalIPTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CopyLocalIPButton = ((System.Windows.Controls.Button)(target));
            
            #line 120 "..\..\..\IPInfoDialog.xaml"
            this.CopyLocalIPButton.Click += new System.Windows.RoutedEventHandler(this.CopyLocalIP_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ExternalIPTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.CopyExternalIPButton = ((System.Windows.Controls.Button)(target));
            
            #line 135 "..\..\..\IPInfoDialog.xaml"
            this.CopyExternalIPButton.Click += new System.Windows.RoutedEventHandler(this.CopyExternalIP_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SubnetTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.ServerStatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.PortTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ConnectedDevicesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.DevicesListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 14:
            this.ExternalNetworksCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.ExternalNetworksListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 16:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 249 "..\..\..\IPInfoDialog.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.Refresh_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 253 "..\..\..\IPInfoDialog.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

