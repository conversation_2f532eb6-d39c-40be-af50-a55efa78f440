using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Sockets;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace InzoIB_Simple
{
    /// <summary>
    /// فئة لتمثيل معلومات الشبكة الخارجية للعرض
    /// </summary>
    public class ExternalNetworkDisplayInfo
    {
        public string IP { get; set; }
        public string Description { get; set; }
        public string StatusIcon { get; set; }
    }

    /// <summary>
    /// نافذة معلومات IP والشبكة مع إمكانية النسخ
    /// </summary>
    public partial class IPInfoDialog : Window
    {
        private readonly MainWindow mainWindow;
        private string externalIP = "";

        public IPInfoDialog(MainWindow parent)
        {
            InitializeComponent();
            mainWindow = parent;
            LoadIPInfo();
        }

        /// <summary>
        /// تحميل معلومات IP
        /// </summary>
        private async void LoadIPInfo()
        {
            try
            {
                // معلومات الجهاز
                MachineNameTextBox.Text = Environment.MachineName;
                UserNameTextBox.Text = Environment.UserName;
                OSVersionTextBox.Text = Environment.OSVersion.ToString();

                // العنوان المحلي
                var localIP = GetLocalIPAddress();
                LocalIPTextBox.Text = localIP ?? "غير متاح";

                // الشبكة المحلية
                SubnetTextBox.Text = GetSubnetInfo(localIP) ?? "غير متاح";

                // معلومات المزامنة
                LoadSyncInfo();

                // الشبكات الخارجية
                LoadExternalNetworks();

                // العنوان الخارجي (غير متزامن)
                ExternalIPTextBox.Text = "جاري التحميل...";
                await LoadExternalIP();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعلومات:\n{ex.Message}", "خطأ", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل العنوان الخارجي
        /// </summary>
        private async Task LoadExternalIP()
        {
            try
            {
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(10);
                    externalIP = await client.GetStringAsync("https://api.ipify.org");
                    ExternalIPTextBox.Text = externalIP.Trim();
                }
            }
            catch
            {
                externalIP = "";
                ExternalIPTextBox.Text = "غير متاح (لا يوجد اتصال إنترنت)";
            }
        }

        /// <summary>
        /// تحميل معلومات المزامنة
        /// </summary>
        private void LoadSyncInfo()
        {
            try
            {
                var networkSyncManager = mainWindow.GetNetworkSyncManager();
                
                if (networkSyncManager != null)
                {
                    ServerStatusTextBlock.Text = "✅ يعمل";
                    ServerStatusTextBlock.Foreground = System.Windows.Media.Brushes.LightGreen;
                    PortTextBlock.Text = "8080 (أو بديل)";
                    
                    var connectedDevices = networkSyncManager.GetConnectedDevices();
                    ConnectedDevicesTextBlock.Text = connectedDevices.Count.ToString();
                    
                    DevicesListBox.ItemsSource = connectedDevices;
                }
                else
                {
                    ServerStatusTextBlock.Text = "❌ غير نشط";
                    ServerStatusTextBlock.Foreground = System.Windows.Media.Brushes.LightCoral;
                    PortTextBlock.Text = "غير متاح";
                    ConnectedDevicesTextBlock.Text = "0";
                    DevicesListBox.ItemsSource = null;
                }
            }
            catch (Exception ex)
            {
                ServerStatusTextBlock.Text = $"❌ خطأ: {ex.Message}";
                ServerStatusTextBlock.Foreground = System.Windows.Media.Brushes.LightCoral;
            }
        }

        /// <summary>
        /// تحميل الشبكات الخارجية
        /// </summary>
        private void LoadExternalNetworks()
        {
            try
            {
                var externalNetworks = mainWindow.GetExternalNetworks();
                
                if (externalNetworks != null && externalNetworks.Count > 0)
                {
                    ExternalNetworksCountTextBlock.Text = externalNetworks.Count.ToString();
                    
                    var displayList = externalNetworks.Select(network => new ExternalNetworkDisplayInfo
                    {
                        IP = network.IP,
                        Description = network.Description,
                        StatusIcon = network.IsOnline ? "✅" : "❌"
                    }).ToList();
                    
                    ExternalNetworksListBox.ItemsSource = displayList;
                }
                else
                {
                    ExternalNetworksCountTextBlock.Text = "0";
                    ExternalNetworksListBox.ItemsSource = null;
                }
            }
            catch (Exception ex)
            {
                ExternalNetworksCountTextBlock.Text = $"خطأ: {ex.Message}";
            }
        }

        /// <summary>
        /// نسخ اسم الجهاز
        /// </summary>
        private void CopyMachineName_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var machineName = MachineNameTextBox.Text;
                if (!string.IsNullOrEmpty(machineName))
                {
                    Clipboard.SetText(machineName);
                    ShowCopySuccess("تم نسخ اسم الجهاز", sender as Button);
                }
            }
            catch (Exception ex)
            {
                ShowCopyError($"خطأ في نسخ اسم الجهاز: {ex.Message}");
            }
        }

        /// <summary>
        /// نسخ العنوان المحلي
        /// </summary>
        private void CopyLocalIP_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var localIP = LocalIPTextBox.Text;
                if (!string.IsNullOrEmpty(localIP) && localIP != "غير متاح")
                {
                    Clipboard.SetText(localIP);
                    ShowCopySuccess("تم نسخ العنوان المحلي", sender as Button);
                }
            }
            catch (Exception ex)
            {
                ShowCopyError($"خطأ في نسخ العنوان المحلي: {ex.Message}");
            }
        }

        /// <summary>
        /// نسخ العنوان الخارجي
        /// </summary>
        private void CopyExternalIP_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(externalIP))
                {
                    Clipboard.SetText(externalIP);
                    ShowCopySuccess("تم نسخ العنوان الخارجي", sender as Button);
                }
                else
                {
                    ShowCopyError("العنوان الخارجي غير متاح للنسخ");
                }
            }
            catch (Exception ex)
            {
                ShowCopyError($"خطأ في نسخ العنوان الخارجي: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث المعلومات
        /// </summary>
        private async void Refresh_Click(object sender, RoutedEventArgs e)
        {
            RefreshButton.IsEnabled = false;
            RefreshButton.Content = "🔄 جاري التحديث...";
            
            try
            {
                LoadIPInfo();
                await Task.Delay(1000); // انتظار قصير للتأكد من اكتمال التحديث
            }
            finally
            {
                RefreshButton.IsEnabled = true;
                RefreshButton.Content = "🔄 تحديث";
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// عرض رسالة نجاح النسخ
        /// </summary>
        private void ShowCopySuccess(string message, Button sourceButton = null)
        {
            // تغيير لون الزر مؤقتاً للإشارة للنجاح
            var button = sourceButton;
            if (button != null)
            {
                var originalContent = button.Content;
                var originalBackground = button.Background;
                
                button.Content = "✅ تم النسخ";
                button.Background = System.Windows.Media.Brushes.Green;
                
                // إعادة الحالة الأصلية بعد ثانيتين
                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(2);
                timer.Tick += (s, e) =>
                {
                    button.Content = originalContent;
                    button.Background = originalBackground;
                    timer.Stop();
                };
                timer.Start();
            }
            
            // رسالة في شريط العنوان
            var originalTitle = Title;
            Title = $"✅ {message}";
            
            var titleTimer = new System.Windows.Threading.DispatcherTimer();
            titleTimer.Interval = TimeSpan.FromSeconds(3);
            titleTimer.Tick += (s, e) =>
            {
                Title = originalTitle;
                titleTimer.Stop();
            };
            titleTimer.Start();
        }

        /// <summary>
        /// عرض رسالة خطأ النسخ
        /// </summary>
        private void ShowCopyError(string message)
        {
            MessageBox.Show(message, "خطأ في النسخ", MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// الحصول على عنوان IP المحلي
        /// </summary>
        private string GetLocalIPAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        return ip.ToString();
                    }
                }
            }
            catch { }
            
            return null;
        }

        /// <summary>
        /// الحصول على معلومات الشبكة الفرعية
        /// </summary>
        private string GetSubnetInfo(string ip)
        {
            try
            {
                if (!string.IsNullOrEmpty(ip))
                {
                    var parts = ip.Split('.');
                    if (parts.Length >= 3)
                    {
                        return $"{parts[0]}.{parts[1]}.{parts[2]}.x";
                    }
                }
            }
            catch { }
            
            return null;
        }
    }
}
