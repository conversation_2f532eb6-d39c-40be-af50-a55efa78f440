using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.IO;
using System.Text;
using System.Security.Cryptography;
using System.Net.Http;
using System.Diagnostics;

namespace InzoIB_Simple
{
    /// <summary>
    /// مدير الاتصال مع Cloud Firestore باستخدام REST API
    /// </summary>
    public class FirestoreManager
    {
        private HttpClient httpClient;
        private string projectId;
        private string apiKey;
        private string collectionName;
        private bool isInitialized = false;
        private string baseUrl;

        // مسار حفظ إعدادات Firestore
        private readonly string firestoreSettingsPath;

        // إعدادات الأمان والمصادقة
        private string authorizedUID;
        private string deviceUID;
        private string authToken;
        private DateTime tokenExpiry;
        private readonly string securityKey = "InzoIB_Security_2024";

        /// <summary>
        /// حدث يتم تشغيله عند تغيير حالة الاتصال
        /// </summary>
        public event Action<bool, string> ConnectionStatusChanged;

        /// <summary>
        /// حدث يتم تشغيله عند تحديث البيانات
        /// </summary>
        public event Action<List<ContentItem>> DataUpdated;

        /// <summary>
        /// إنشاء مدير Firestore جديد
        /// </summary>
        /// <param name="settingsPath">مسار ملف الإعدادات</param>
        public FirestoreManager(string settingsPath)
        {
            firestoreSettingsPath = settingsPath;
            httpClient = new HttpClient();
            deviceUID = GenerateDeviceUID();
            LoadSettings();
        }

        /// <summary>
        /// توليد معرف فريد للجهاز
        /// </summary>
        /// <returns>معرف الجهاز</returns>
        private string GenerateDeviceUID()
        {
            try
            {
                var machineInfo = Environment.MachineName + Environment.UserName + Environment.OSVersion.ToString();
                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo + securityKey));
                    return Convert.ToBase64String(hash).Substring(0, 16);
                }
            }
            catch
            {
                return Guid.NewGuid().ToString("N").Substring(0, 16);
            }
        }

        /// <summary>
        /// تعيين UID المصرح له بالتعديل
        /// </summary>
        /// <param name="uid">معرف المستخدم المصرح</param>
        public void SetAuthorizedUID(string uid)
        {
            authorizedUID = uid;
            SaveSettings();
            Debug.WriteLine($"🔐 تم تعيين UID المصرح: {uid}");
        }

        /// <summary>
        /// التحقق من صلاحية التعديل
        /// </summary>
        /// <returns>true إذا كان مصرحاً بالتعديل</returns>
        private bool IsAuthorizedToModify()
        {
            if (string.IsNullOrEmpty(authorizedUID))
            {
                Debug.WriteLine("⚠️ لم يتم تعيين UID مصرح - السماح بالتعديل");
                return true; // السماح إذا لم يتم تعيين UID
            }

            bool isAuthorized = deviceUID == authorizedUID;
            Debug.WriteLine($"🔐 فحص الصلاحية: Device={deviceUID}, Authorized={authorizedUID}, Result={isAuthorized}");
            return isAuthorized;
        }

        /// <summary>
        /// توليد رمز مصادقة
        /// </summary>
        /// <returns>رمز المصادقة</returns>
        private string GenerateAuthToken()
        {
            try
            {
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
                var data = $"{deviceUID}:{timestamp}:{securityKey}";

                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(data));
                    authToken = Convert.ToBase64String(hash);
                    tokenExpiry = DateTime.UtcNow.AddHours(24); // صالح لـ 24 ساعة
                    return authToken;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في توليد رمز المصادقة: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// التحقق من صحة رمز المصادقة
        /// </summary>
        /// <returns>true إذا كان الرمز صحيحاً</returns>
        private bool IsTokenValid()
        {
            return !string.IsNullOrEmpty(authToken) && DateTime.UtcNow < tokenExpiry;
        }

        /// <summary>
        /// تحميل إعدادات Firestore
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                if (File.Exists(firestoreSettingsPath))
                {
                    var encryptedSettings = File.ReadAllText(firestoreSettingsPath);
                    var settingsJson = DecryptString(encryptedSettings);
                    var settings = Newtonsoft.Json.JsonConvert.DeserializeObject<FirestoreSettings>(settingsJson);

                    if (settings != null)
                    {
                        projectId = settings.ProjectId;
                        apiKey = settings.ApiKey;
                        collectionName = settings.CollectionName;

                        // تحميل إعدادات الأمان
                        authorizedUID = settings.AuthorizedUID;
                        authToken = settings.AuthToken;

                        if (settings.TokenExpiry.HasValue)
                        {
                            tokenExpiry = settings.TokenExpiry.Value;
                        }

                        Debug.WriteLine("✅ تم تحميل إعدادات Firestore والأمان بنجاح");
                        Debug.WriteLine($"🔐 Device UID: {deviceUID}");
                        Debug.WriteLine($"🔐 Authorized UID: {authorizedUID ?? "غير محدد"}");
                    }
                }
                else
                {
                    Debug.WriteLine("⚠️ ملف إعدادات Firestore غير موجود");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تحميل إعدادات Firestore: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ إعدادات Firestore
        /// </summary>
        public void SaveSettings(string projectId, string apiKey, string collectionName)
        {
            try
            {
                this.projectId = projectId;
                this.apiKey = apiKey;
                this.collectionName = collectionName;

                var settings = new FirestoreSettings
                {
                    ProjectId = projectId,
                    ApiKey = apiKey,
                    CollectionName = collectionName,
                    AuthorizedUID = this.authorizedUID,
                    AuthToken = this.authToken,
                    TokenExpiry = this.tokenExpiry
                };

                var settingsJson = Newtonsoft.Json.JsonConvert.SerializeObject(settings);
                var encryptedSettings = EncryptString(settingsJson);

                // إنشاء مجلد الإعدادات إذا لم يكن موجوداً
                var settingsFolder = Path.GetDirectoryName(firestoreSettingsPath);
                if (!Directory.Exists(settingsFolder))
                {
                    Directory.CreateDirectory(settingsFolder);
                }

                File.WriteAllText(firestoreSettingsPath, encryptedSettings);
                Debug.WriteLine("✅ تم حفظ إعدادات Firestore والأمان بنجاح");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في حفظ إعدادات Firestore: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ إعدادات الأمان فقط
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                if (!string.IsNullOrEmpty(projectId))
                {
                    SaveSettings(projectId, apiKey, collectionName);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في حفظ إعدادات الأمان: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة الاتصال مع Firestore
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                Debug.WriteLine("🔄 بدء تهيئة Firestore...");

                if (string.IsNullOrEmpty(projectId))
                {
                    var errorMessage = "لم يتم تعيين معرف المشروع";
                    Debug.WriteLine($"❌ {errorMessage}");
                    ConnectionStatusChanged?.Invoke(false, errorMessage);
                    return false;
                }

                if (string.IsNullOrEmpty(apiKey))
                {
                    var errorMessage = "لم يتم تعيين مفتاح API";
                    Debug.WriteLine($"❌ {errorMessage}");
                    ConnectionStatusChanged?.Invoke(false, errorMessage);
                    return false;
                }

                // إنشاء URL الأساسي لـ Firestore REST API
                baseUrl = $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)/documents";
                Debug.WriteLine($"🔗 URL الأساسي: {baseUrl}");

                // إعداد HttpClient
                httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
                httpClient.Timeout = TimeSpan.FromSeconds(30);

                // اختبار الاتصال
                Debug.WriteLine("🔄 اختبار الاتصال...");
                var testResult = await TestConnectionAsync();

                if (testResult)
                {
                    isInitialized = true;
                    ConnectionStatusChanged?.Invoke(true, "تم الاتصال بـ Firestore بنجاح");
                    Debug.WriteLine("✅ تم تهيئة Firestore بنجاح");
                    return true;
                }
                else
                {
                    isInitialized = false;
                    Debug.WriteLine("❌ فشل في تهيئة Firestore");
                    return false;
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ في تهيئة Firestore: {ex.Message}";
                ConnectionStatusChanged?.Invoke(false, errorMessage);
                Debug.WriteLine($"❌ {errorMessage}");
                Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال مع Firestore باستخدام REST API
        /// </summary>
        private async Task<bool> TestConnectionAsync()
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrEmpty(projectId))
                {
                    Debug.WriteLine("❌ معرف المشروع مطلوب");
                    ConnectionStatusChanged?.Invoke(false, "معرف المشروع مطلوب");
                    return false;
                }

                if (string.IsNullOrEmpty(apiKey))
                {
                    Debug.WriteLine("❌ مفتاح API مطلوب");
                    ConnectionStatusChanged?.Invoke(false, "مفتاح API مطلوب");
                    return false;
                }

                Debug.WriteLine($"🔄 اختبار الاتصال مع المشروع: {projectId}");

                // جرب عدة URLs للاختبار
                var testUrls = new[]
                {
                    $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)?key={apiKey}",
                    $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)/documents?key={apiKey}",
                    $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)/documents/{collectionName}?key={apiKey}"
                };

                HttpResponseMessage response = null;
                string responseContent = "";
                string workingUrl = "";

                // إضافة headers مطلوبة
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
                httpClient.DefaultRequestHeaders.Add("User-Agent", "InzoIB-Simple/1.0");

                // جرب كل URL
                foreach (var testUrl in testUrls)
                {
                    try
                    {
                        Debug.WriteLine($"🔗 جاري اختبار URL: {testUrl}");
                        response = await httpClient.GetAsync(testUrl);
                        responseContent = await response.Content.ReadAsStringAsync();

                        Debug.WriteLine($"📡 رمز الاستجابة لـ {testUrl}: {response.StatusCode}");
                        Debug.WriteLine($"📄 محتوى الاستجابة: {responseContent.Substring(0, Math.Min(200, responseContent.Length))}...");

                        if (response.IsSuccessStatusCode)
                        {
                            workingUrl = testUrl;
                            break;
                        }
                    }
                    catch (Exception urlEx)
                    {
                        Debug.WriteLine($"❌ خطأ في URL {testUrl}: {urlEx.Message}");
                        continue;
                    }
                }

                if (response != null && response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"✅ تم اختبار الاتصال مع Firestore بنجاح باستخدام: {workingUrl}");
                    ConnectionStatusChanged?.Invoke(true, "تم الاتصال بنجاح!");
                    return true;
                }
                else if (response != null)
                {
                    Debug.WriteLine($"📡 رمز الاستجابة النهائي: {response.StatusCode}");
                    Debug.WriteLine($"📄 استجابة الخادم النهائية: {responseContent}");

                    var errorMessage = $"فشل الاتصال: {response.StatusCode}";

                    // تحليل رسالة الخطأ بناءً على المحتوى
                    if (responseContent.Contains("API key not valid") || responseContent.Contains("invalid"))
                    {
                        errorMessage = "مفتاح API غير صالح - تحقق من صحة المفتاح";
                    }
                    else if (responseContent.Contains("not found") || response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        errorMessage = "معرف المشروع غير صحيح أو المشروع غير موجود";
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.Forbidden)
                    {
                        errorMessage = "مفتاح API لا يملك الصلاحيات المطلوبة - تحقق من تفعيل Firestore API";
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                    {
                        errorMessage = "مفتاح API غير مصرح له بالوصول";
                    }
                    else if (responseContent.Contains("quota") || responseContent.Contains("limit"))
                    {
                        errorMessage = "تم تجاوز حد الاستخدام - انتظر قليلاً وأعد المحاولة";
                    }

                    Debug.WriteLine($"❌ {errorMessage}");
                    ConnectionStatusChanged?.Invoke(false, errorMessage);
                    return false;
                }
                else
                {
                    var errorMessage = "فشل في الوصول إلى جميع URLs - تحقق من الإنترنت والإعدادات";
                    Debug.WriteLine($"❌ {errorMessage}");
                    ConnectionStatusChanged?.Invoke(false, errorMessage);
                    return false;
                }
            }
            catch (HttpRequestException httpEx)
            {
                var errorMessage = "خطأ في الشبكة - تحقق من اتصال الإنترنت";
                Debug.WriteLine($"❌ خطأ HTTP: {httpEx.Message}");
                ConnectionStatusChanged?.Invoke(false, errorMessage);
                return false;
            }
            catch (TaskCanceledException timeoutEx)
            {
                var errorMessage = "انتهت مهلة الاتصال - تحقق من اتصال الإنترنت";
                Debug.WriteLine($"❌ انتهاء المهلة: {timeoutEx.Message}");
                ConnectionStatusChanged?.Invoke(false, errorMessage);
                return false;
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ غير متوقع: {ex.Message}";
                Debug.WriteLine($"❌ خطأ عام: {ex.Message}");
                ConnectionStatusChanged?.Invoke(false, errorMessage);
                return false;
            }
        }

        /// <summary>
        /// حفظ البيانات في Firestore باستخدام REST API
        /// </summary>
        public async Task<bool> SaveDataAsync(List<ContentItem> items)
        {
            try
            {
                // فحص الصلاحيات أولاً
                if (!IsAuthorizedToModify())
                {
                    Debug.WriteLine("🚫 غير مصرح بالتعديل - تم رفض العملية");
                    ConnectionStatusChanged?.Invoke(false, "غير مصرح بالتعديل");
                    return false;
                }

                // التحقق من صحة البيانات
                if (items == null || items.Count == 0)
                {
                    Debug.WriteLine("⚠️ قائمة العناصر فارغة أو null");
                    ConnectionStatusChanged?.Invoke(false, "لا توجد بيانات للحفظ");
                    return false;
                }

                if (!isInitialized)
                {
                    Debug.WriteLine("🔄 إعادة تهيئة الاتصال مع Firestore للحفظ...");
                    await InitializeAsync();
                    if (!isInitialized)
                    {
                        Debug.WriteLine("❌ فشل في تهيئة الاتصال مع Firestore للحفظ");
                        ConnectionStatusChanged?.Invoke(false, "فشل في تهيئة الاتصال للحفظ");
                        return false;
                    }
                }

                // التحقق من اسم المجموعة
                if (string.IsNullOrEmpty(collectionName))
                {
                    Debug.WriteLine("❌ اسم المجموعة غير محدد للحفظ");
                    ConnectionStatusChanged?.Invoke(false, "اسم المجموعة غير محدد");
                    return false;
                }

                // توليد أو تجديد رمز المصادقة
                if (!IsTokenValid())
                {
                    GenerateAuthToken();
                }

                Debug.WriteLine($"🔄 بدء حفظ {items.Count} عنصر في Firestore...");
                ConnectionStatusChanged?.Invoke(true, $"جاري حفظ {items.Count} عنصر...");

                // حفظ كل عنصر باستخدام REST API مع معالجة أفضل للأخطاء
                var successCount = 0;
                var failedItems = new List<string>();

                for (int i = 0; i < items.Count; i++)
                {
                    var item = items[i];

                    // التأكد من وجود معرف فريد
                    if (string.IsNullOrEmpty(item.Id))
                    {
                        item.Id = Guid.NewGuid().ToString();
                        Debug.WriteLine($"🆔 تم إنشاء معرف جديد للعنصر: {item.Id}");
                    }

                    try
                    {
                        var success = await SaveSingleItemAsync(item);
                        if (success)
                        {
                            successCount++;
                            Debug.WriteLine($"✅ تم حفظ العنصر {i + 1}/{items.Count}: {item.Id}");
                        }
                        else
                        {
                            failedItems.Add(item.Id);
                            Debug.WriteLine($"❌ فشل حفظ العنصر {i + 1}/{items.Count}: {item.Id}");
                        }
                    }
                    catch (Exception itemEx)
                    {
                        failedItems.Add(item.Id);
                        Debug.WriteLine($"❌ خطأ في حفظ العنصر {item.Id}: {itemEx.Message}");
                    }

                    // تحديث حالة التقدم
                    if ((i + 1) % 5 == 0 || i == items.Count - 1)
                    {
                        ConnectionStatusChanged?.Invoke(true, $"تم حفظ {successCount}/{items.Count} عنصر");
                    }
                }

                // تقرير النتائج
                if (successCount == items.Count)
                {
                    Debug.WriteLine($"✅ تم حفظ جميع العناصر ({successCount}) في Firestore بنجاح");
                    ConnectionStatusChanged?.Invoke(true, $"تم حفظ جميع العناصر ({successCount}) بنجاح");
                    return true;
                }
                else if (successCount > 0)
                {
                    Debug.WriteLine($"⚠️ تم حفظ {successCount}/{items.Count} عنصر في Firestore");
                    ConnectionStatusChanged?.Invoke(true, $"تم حفظ {successCount}/{items.Count} عنصر");

                    if (failedItems.Count > 0)
                    {
                        Debug.WriteLine($"❌ فشل حفظ العناصر: {string.Join(", ", failedItems)}");
                    }

                    return true; // نعتبر النجاح الجزئي نجاحاً
                }
                else
                {
                    Debug.WriteLine("❌ فشل في حفظ جميع العناصر");
                    ConnectionStatusChanged?.Invoke(false, "فشل في حفظ البيانات");
                    return false;
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ عام في حفظ البيانات: {ex.Message}";
                Debug.WriteLine($"❌ {errorMessage}");
                Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                ConnectionStatusChanged?.Invoke(false, errorMessage);
                return false;
            }
        }

        /// <summary>
        /// حفظ عنصر واحد في Firestore
        /// </summary>
        private async Task<bool> SaveSingleItemAsync(ContentItem item)
        {
            try
            {
                var documentUrl = $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)/documents/{collectionName}/{item.Id}?key={apiKey}";

                Debug.WriteLine($"🔄 حفظ العنصر: {item.Id} - {item.Title}");

                // تحويل العنصر إلى تنسيق Firestore مع معالجة أفضل للأخطاء
                var firestoreDocument = ConvertToFirestoreDocument(item);
                if (firestoreDocument == null)
                {
                    Debug.WriteLine($"❌ فشل في تحويل العنصر {item.Id} إلى تنسيق Firestore");
                    return false;
                }

                var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(firestoreDocument, Newtonsoft.Json.Formatting.None);

                Debug.WriteLine($"📄 محتوى JSON للعنصر {item.Id}: {jsonContent.Substring(0, Math.Min(200, jsonContent.Length))}...");

                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // إعداد headers مطلوبة
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
                httpClient.DefaultRequestHeaders.Add("User-Agent", "InzoIB-Simple/1.0");

                // استخدام PATCH لإنشاء أو تحديث المستند
                var response = await httpClient.PatchAsync(documentUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                Debug.WriteLine($"📡 رمز الاستجابة للعنصر {item.Id}: {response.StatusCode}");

                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"📄 استجابة الخادم للعنصر {item.Id}: {responseContent}");
                }

                if (response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"✅ تم حفظ العنصر {item.Id} بنجاح");
                    return true;
                }
                else
                {
                    var errorMessage = GetDetailedErrorMessage(response.StatusCode, responseContent);
                    Debug.WriteLine($"❌ فشل حفظ العنصر {item.Id}: {errorMessage}");
                    return false;
                }
            }
            catch (HttpRequestException httpEx)
            {
                Debug.WriteLine($"❌ خطأ شبكة في حفظ العنصر {item.Id}: {httpEx.Message}");
                return false;
            }
            catch (TaskCanceledException timeoutEx)
            {
                Debug.WriteLine($"❌ انتهاء مهلة حفظ العنصر {item.Id}: {timeoutEx.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ عام في حفظ العنصر {item.Id}: {ex.Message}");
                Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// تحويل ContentItem إلى تنسيق Firestore Document
        /// </summary>
        private object ConvertToFirestoreDocument(ContentItem item)
        {
            try
            {
                if (item == null)
                {
                    Debug.WriteLine("❌ العنصر المراد تحويله null");
                    return null;
                }

                // تحويل التاريخ إلى تنسيق ISO 8601 مع معالجة أفضل
                string createdDateString;
                try
                {
                    createdDateString = item.CreatedDate.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                }
                catch
                {
                    createdDateString = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                    Debug.WriteLine("⚠️ تم استخدام التاريخ الحالي بدلاً من تاريخ العنصر");
                }

                // توليد أو تجديد رمز المصادقة
                if (!IsTokenValid())
                {
                    GenerateAuthToken();
                }

                // إنشاء كائن Firestore مع التأكد من صحة جميع القيم ومعلومات الأمان
                var firestoreDoc = new
                {
                    fields = new
                    {
                        Id = new { stringValue = item.Id ?? Guid.NewGuid().ToString() },
                        Section = new { stringValue = item.Section ?? "" },
                        Title = new { stringValue = item.Title ?? "" },
                        Content = new { stringValue = item.Content ?? "" },
                        CreatedDate = new { timestampValue = createdDateString },
                        IsDivider = new { booleanValue = item.IsDivider },
                        DividerText = new { stringValue = item.DividerText ?? "" },
                        IsTelegramMessage = new { booleanValue = item.IsTelegramMessage },
                        TelegramSender = new { stringValue = item.TelegramSender ?? "" },
                        TelegramMessageId = new { integerValue = item.TelegramMessageId.ToString() },
                        TelegramChatId = new { integerValue = item.TelegramChatId.ToString() },

                        // معلومات الأمان والمصادقة
                        DeviceUID = new { stringValue = deviceUID ?? "" },
                        AuthToken = new { stringValue = authToken ?? "" },
                        LastModified = new { timestampValue = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") },
                        ModifiedBy = new { stringValue = Environment.MachineName ?? "" }
                    }
                };

                Debug.WriteLine($"✅ تم تحويل العنصر {item.Id} إلى تنسيق Firestore بنجاح");
                return firestoreDoc;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تحويل العنصر إلى تنسيق Firestore: {ex.Message}");
                Debug.WriteLine($"❌ تفاصيل العنصر: Id={item?.Id}, Title={item?.Title}");

                // إرجاع كائن بسيط في حالة الخطأ
                try
                {
                    return new
                    {
                        fields = new
                        {
                            Id = new { stringValue = item?.Id ?? Guid.NewGuid().ToString() },
                            Section = new { stringValue = item?.Section ?? "غير محدد" },
                            Title = new { stringValue = item?.Title ?? "عنصر بدون عنوان" },
                            Content = new { stringValue = item?.Content ?? "" },
                            CreatedDate = new { timestampValue = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") },
                            IsDivider = new { booleanValue = item?.IsDivider ?? false },
                            DividerText = new { stringValue = item?.DividerText ?? "" },
                            IsTelegramMessage = new { booleanValue = item?.IsTelegramMessage ?? false },
                            TelegramSender = new { stringValue = item?.TelegramSender ?? "" },
                            TelegramMessageId = new { integerValue = (item?.TelegramMessageId ?? 0).ToString() },
                            TelegramChatId = new { integerValue = (item?.TelegramChatId ?? 0).ToString() }
                        }
                    };
                }
                catch
                {
                    Debug.WriteLine("❌ فشل في إنشاء كائن بديل");
                    return null;
                }
            }
        }

        /// <summary>
        /// حذف جميع المستندات في مجموعة (مبسط للـ REST API)
        /// </summary>
        private async Task DeleteAllDocumentsAsync()
        {
            try
            {
                // للبساطة، سنتجاهل حذف المستندات القديمة في الوقت الحالي
                // يمكن تطوير هذا لاحقاً إذا لزم الأمر
                Debug.WriteLine("ℹ️ تم تجاهل حذف المستندات القديمة للبساطة");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في حذف المستندات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل البيانات من Firestore باستخدام REST API
        /// </summary>
        public async Task<List<ContentItem>> LoadDataAsync()
        {
            try
            {
                if (!isInitialized)
                {
                    Debug.WriteLine("🔄 إعادة تهيئة الاتصال مع Firestore للتحميل...");
                    await InitializeAsync();
                    if (!isInitialized)
                    {
                        Debug.WriteLine("❌ فشل في تهيئة الاتصال مع Firestore للتحميل");
                        ConnectionStatusChanged?.Invoke(false, "فشل في تهيئة الاتصال للتحميل");
                        return new List<ContentItem>();
                    }
                }

                // التحقق من اسم المجموعة
                if (string.IsNullOrEmpty(collectionName))
                {
                    Debug.WriteLine("❌ اسم المجموعة غير محدد للتحميل");
                    ConnectionStatusChanged?.Invoke(false, "اسم المجموعة غير محدد");
                    return new List<ContentItem>();
                }

                // قراءة المجموعة باستخدام REST API مع معالجة أفضل
                var collectionUrl = $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)/documents/{collectionName}?key={apiKey}";

                Debug.WriteLine($"🔄 تحميل البيانات من: {collectionUrl}");

                // إعداد headers مطلوبة
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
                httpClient.DefaultRequestHeaders.Add("User-Agent", "InzoIB-Simple/1.0");

                var response = await httpClient.GetAsync(collectionUrl);
                var responseContent = await response.Content.ReadAsStringAsync();

                Debug.WriteLine($"📡 رمز الاستجابة: {response.StatusCode}");
                Debug.WriteLine($"📄 محتوى الاستجابة (أول 1000 حرف): {responseContent.Substring(0, Math.Min(1000, responseContent.Length))}");

                if (!response.IsSuccessStatusCode)
                {
                    if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        Debug.WriteLine("ℹ️ المجموعة غير موجودة - سيتم إنشاؤها عند الحفظ");
                        ConnectionStatusChanged?.Invoke(true, "المجموعة فارغة - جاهز للحفظ");
                        return new List<ContentItem>();
                    }
                    else
                    {
                        var errorMessage = GetDetailedErrorMessage(response.StatusCode, responseContent);
                        Debug.WriteLine($"❌ فشل في قراءة المجموعة: {errorMessage}");
                        ConnectionStatusChanged?.Invoke(false, errorMessage);
                        return new List<ContentItem>();
                    }
                }

                var items = ParseFirestoreResponse(responseContent);

                if (items.Count > 0)
                {
                    Debug.WriteLine($"✅ تم تحميل {items.Count} عنصر من Firestore بنجاح");
                    ConnectionStatusChanged?.Invoke(true, $"تم تحميل {items.Count} عنصر بنجاح");
                    DataUpdated?.Invoke(items);
                }
                else
                {
                    Debug.WriteLine("ℹ️ لم يتم العثور على بيانات في Firestore");
                    ConnectionStatusChanged?.Invoke(true, "المجموعة فارغة");
                }

                return items;
            }
            catch (HttpRequestException httpEx)
            {
                var errorMessage = "خطأ في الشبكة - تحقق من اتصال الإنترنت";
                Debug.WriteLine($"❌ خطأ HTTP في التحميل: {httpEx.Message}");
                ConnectionStatusChanged?.Invoke(false, errorMessage);
                return new List<ContentItem>();
            }
            catch (TaskCanceledException timeoutEx)
            {
                var errorMessage = "انتهت مهلة الاتصال - تحقق من اتصال الإنترنت";
                Debug.WriteLine($"❌ انتهاء المهلة في التحميل: {timeoutEx.Message}");
                ConnectionStatusChanged?.Invoke(false, errorMessage);
                return new List<ContentItem>();
            }
            catch (Exception ex)
            {
                var errorMessage = $"خطأ في التحميل: {ex.Message}";
                Debug.WriteLine($"❌ خطأ عام في تحميل البيانات من Firestore: {ex.Message}");
                Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                ConnectionStatusChanged?.Invoke(false, errorMessage);
                return new List<ContentItem>();
            }
        }

        /// <summary>
        /// الحصول على رسالة خطأ مفصلة بناءً على رمز الاستجابة والمحتوى
        /// </summary>
        private string GetDetailedErrorMessage(System.Net.HttpStatusCode statusCode, string responseContent)
        {
            try
            {
                switch (statusCode)
                {
                    case System.Net.HttpStatusCode.Unauthorized:
                        return "مفتاح API غير مصرح له بالوصول - تحقق من صحة المفتاح";
                    case System.Net.HttpStatusCode.Forbidden:
                        if (responseContent.Contains("API key not valid"))
                            return "مفتاح API غير صالح - تحقق من نسخ المفتاح بشكل صحيح";
                        else if (responseContent.Contains("not enabled"))
                            return "Firestore API غير مفعل في المشروع - فعل API من Google Cloud Console";
                        else if (responseContent.Contains("permission"))
                            return "لا توجد صلاحيات للوصول - تحقق من قواعد الأمان في Firestore";
                        else
                            return "لا توجد صلاحيات للوصول - تحقق من إعدادات المشروع";
                    case System.Net.HttpStatusCode.NotFound:
                        return "المشروع أو المجموعة غير موجودة - تحقق من معرف المشروع";
                    case System.Net.HttpStatusCode.TooManyRequests:
                        return "تم تجاوز حد الطلبات - انتظر قليلاً وأعد المحاولة";
                    case System.Net.HttpStatusCode.BadRequest:
                        return "طلب غير صالح - تحقق من تنسيق البيانات";
                    case System.Net.HttpStatusCode.InternalServerError:
                        return "خطأ في خادم Google - أعد المحاولة لاحقاً";
                    case System.Net.HttpStatusCode.ServiceUnavailable:
                        return "خدمة Firestore غير متاحة مؤقتاً - أعد المحاولة لاحقاً";
                    default:
                        return $"خطأ في الخادم ({(int)statusCode}): {statusCode}";
                }
            }
            catch
            {
                return $"خطأ غير معروف: {statusCode}";
            }
        }

        /// <summary>
        /// تشخيص شامل لمشاكل الاتصال مع Firestore
        /// </summary>
        public async Task<string> DiagnoseConnectionAsync()
        {
            var diagnosis = new StringBuilder();
            diagnosis.AppendLine("🔍 تشخيص شامل لاتصال Firestore:");
            diagnosis.AppendLine("=" + new string('=', 50));

            try
            {
                // فحص الإعدادات الأساسية
                diagnosis.AppendLine("📋 فحص الإعدادات:");
                diagnosis.AppendLine($"   • معرف المشروع: {(string.IsNullOrEmpty(projectId) ? "❌ غير محدد" : "✅ محدد")}");
                diagnosis.AppendLine($"   • مفتاح API: {(string.IsNullOrEmpty(apiKey) ? "❌ غير محدد" : "✅ محدد")}");
                diagnosis.AppendLine($"   • اسم المجموعة: {(string.IsNullOrEmpty(collectionName) ? "❌ غير محدد" : "✅ محدد")}");
                diagnosis.AppendLine($"   • حالة التهيئة: {(isInitialized ? "✅ مهيأ" : "❌ غير مهيأ")}");
                diagnosis.AppendLine();

                if (string.IsNullOrEmpty(projectId) || string.IsNullOrEmpty(apiKey))
                {
                    diagnosis.AppendLine("❌ لا يمكن المتابعة - إعدادات أساسية مفقودة");
                    return diagnosis.ToString();
                }

                // فحص الاتصال بالإنترنت
                diagnosis.AppendLine("🌐 فحص الاتصال بالإنترنت:");
                try
                {
                    using (var client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromSeconds(10);
                        var response = await client.GetAsync("https://www.google.com");
                        diagnosis.AppendLine($"   • الاتصال بـ Google: {(response.IsSuccessStatusCode ? "✅ متصل" : "❌ غير متصل")}");
                    }
                }
                catch
                {
                    diagnosis.AppendLine("   • الاتصال بـ Google: ❌ فشل الاتصال");
                }

                // فحص الوصول إلى Firestore API
                diagnosis.AppendLine("🔥 فحص الوصول إلى Firestore:");
                var testUrls = new[]
                {
                    $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)?key={apiKey}",
                    $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)/documents?key={apiKey}",
                    $"https://firestore.googleapis.com/v1/projects/{projectId}/databases/(default)/documents/{collectionName}?key={apiKey}"
                };

                foreach (var url in testUrls)
                {
                    try
                    {
                        using (var client = new HttpClient())
                        {
                            client.Timeout = TimeSpan.FromSeconds(15);
                            var response = await client.GetAsync(url);
                            var content = await response.Content.ReadAsStringAsync();

                            diagnosis.AppendLine($"   • اختبار URL: {response.StatusCode}");

                            if (!response.IsSuccessStatusCode)
                            {
                                var errorMsg = GetDetailedErrorMessage(response.StatusCode, content);
                                diagnosis.AppendLine($"     └─ الخطأ: {errorMsg}");
                            }
                            else
                            {
                                diagnosis.AppendLine($"     └─ ✅ نجح الاتصال");
                                break; // إذا نجح أحد الروابط، توقف
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        diagnosis.AppendLine($"   • اختبار URL: ❌ خطأ - {ex.Message}");
                    }
                }

                // نصائح للحل
                diagnosis.AppendLine();
                diagnosis.AppendLine("💡 نصائح لحل المشاكل:");
                diagnosis.AppendLine("   1. تأكد من تفعيل Firestore API في Google Cloud Console");
                diagnosis.AppendLine("   2. تحقق من صحة مفتاح API وصلاحياته");
                diagnosis.AppendLine("   3. تأكد من إنشاء قاعدة بيانات Firestore في المشروع");
                diagnosis.AppendLine("   4. تحقق من قواعد الأمان في Firebase Console");
                diagnosis.AppendLine("   5. تأكد من اتصال الإنترنت");

            }
            catch (Exception ex)
            {
                diagnosis.AppendLine($"❌ خطأ في التشخيص: {ex.Message}");
            }

            return diagnosis.ToString();
        }

        /// <summary>
        /// تحليل استجابة Firestore وتحويلها إلى قائمة ContentItem
        /// </summary>
        private List<ContentItem> ParseFirestoreResponse(string jsonContent)
        {
            var items = new List<ContentItem>();

            try
            {
                if (string.IsNullOrEmpty(jsonContent))
                {
                    Debug.WriteLine("⚠️ محتوى الاستجابة فارغ");
                    return items;
                }

                Debug.WriteLine($"📄 تحليل محتوى JSON: {jsonContent.Substring(0, Math.Min(500, jsonContent.Length))}...");

                // تحليل JSON باستخدام Newtonsoft.Json مع معالجة أفضل للأخطاء
                var response = Newtonsoft.Json.JsonConvert.DeserializeObject<FirestoreResponse>(jsonContent);

                if (response?.documents != null && response.documents.Count > 0)
                {
                    Debug.WriteLine($"📋 تم العثور على {response.documents.Count} مستند");

                    foreach (var doc in response.documents)
                    {
                        try
                        {
                            var item = ParseFirestoreDocument(doc);
                            if (item != null)
                            {
                                items.Add(item);
                                Debug.WriteLine($"✅ تم تحليل المستند: {item.Id} - {item.Title}");
                            }
                            else
                            {
                                Debug.WriteLine("⚠️ فشل في تحليل مستند - النتيجة null");
                            }
                        }
                        catch (Exception docEx)
                        {
                            Debug.WriteLine($"❌ خطأ في تحليل مستند: {docEx.Message}");
                            Debug.WriteLine($"❌ تفاصيل المستند: {Newtonsoft.Json.JsonConvert.SerializeObject(doc)}");
                        }
                    }
                }
                else
                {
                    Debug.WriteLine("ℹ️ لا توجد مستندات في الاستجابة أو المجموعة فارغة");

                    // محاولة تحليل كاستجابة مستند واحد
                    try
                    {
                        var singleDoc = Newtonsoft.Json.JsonConvert.DeserializeObject<FirestoreDocument>(jsonContent);
                        if (singleDoc?.fields != null)
                        {
                            var item = ParseFirestoreDocumentDirect(singleDoc);
                            if (item != null)
                            {
                                items.Add(item);
                                Debug.WriteLine($"✅ تم تحليل مستند واحد: {item.Id}");
                            }
                        }
                    }
                    catch (Exception singleEx)
                    {
                        Debug.WriteLine($"ℹ️ ليس مستند واحد أيضاً: {singleEx.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تحليل استجابة Firestore: {ex.Message}");
                Debug.WriteLine($"❌ محتوى JSON الذي فشل: {jsonContent}");
                Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }

            Debug.WriteLine($"📊 إجمالي العناصر المحللة: {items.Count}");
            return items;
        }

        /// <summary>
        /// تحليل مستند Firestore واحد وتحويله إلى ContentItem (من dynamic)
        /// </summary>
        private ContentItem ParseFirestoreDocument(dynamic document)
        {
            try
            {
                var fields = document.fields;
                if (fields == null)
                {
                    Debug.WriteLine("⚠️ حقول المستند فارغة");
                    return null;
                }

                var item = new ContentItem
                {
                    Id = GetStringValueFromDynamic(fields.Id),
                    Section = GetStringValueFromDynamic(fields.Section),
                    Title = GetStringValueFromDynamic(fields.Title),
                    Content = GetStringValueFromDynamic(fields.Content),
                    DividerText = GetStringValueFromDynamic(fields.DividerText),
                    TelegramSender = GetStringValueFromDynamic(fields.TelegramSender),
                    IsDivider = GetBoolValueFromDynamic(fields.IsDivider),
                    IsTelegramMessage = GetBoolValueFromDynamic(fields.IsTelegramMessage),
                    TelegramMessageId = (int)GetIntValueFromDynamic(fields.TelegramMessageId),
                    TelegramChatId = GetIntValueFromDynamic(fields.TelegramChatId)
                };

                // تحليل التاريخ مع معالجة أفضل
                item.CreatedDate = GetDateTimeValueFromDynamic(fields.CreatedDate);

                Debug.WriteLine($"✅ تم تحليل المستند بنجاح: {item.Id}");
                return item;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تحليل مستند Firestore: {ex.Message}");
                Debug.WriteLine($"❌ تفاصيل المستند: {Newtonsoft.Json.JsonConvert.SerializeObject(document)}");
                return null;
            }
        }

        /// <summary>
        /// تحليل مستند Firestore واحد وتحويله إلى ContentItem (من FirestoreDocument)
        /// </summary>
        private ContentItem ParseFirestoreDocumentDirect(FirestoreDocument document)
        {
            try
            {
                if (document?.fields == null)
                {
                    Debug.WriteLine("⚠️ حقول المستند فارغة");
                    return null;
                }

                var item = new ContentItem
                {
                    Id = GetStringValueFromField(document.fields.GetValueOrDefault("Id")),
                    Section = GetStringValueFromField(document.fields.GetValueOrDefault("Section")),
                    Title = GetStringValueFromField(document.fields.GetValueOrDefault("Title")),
                    Content = GetStringValueFromField(document.fields.GetValueOrDefault("Content")),
                    DividerText = GetStringValueFromField(document.fields.GetValueOrDefault("DividerText")),
                    TelegramSender = GetStringValueFromField(document.fields.GetValueOrDefault("TelegramSender")),
                    IsDivider = GetBoolValueFromField(document.fields.GetValueOrDefault("IsDivider")),
                    IsTelegramMessage = GetBoolValueFromField(document.fields.GetValueOrDefault("IsTelegramMessage")),
                    TelegramMessageId = (int)GetIntValueFromField(document.fields.GetValueOrDefault("TelegramMessageId")),
                    TelegramChatId = GetIntValueFromField(document.fields.GetValueOrDefault("TelegramChatId"))
                };

                // تحليل التاريخ
                item.CreatedDate = GetDateTimeValueFromField(document.fields.GetValueOrDefault("CreatedDate"));

                Debug.WriteLine($"✅ تم تحليل المستند المباشر بنجاح: {item.Id}");
                return item;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تحليل المستند المباشر: {ex.Message}");
                return null;
            }
        }

        // دوال مساعدة للتعامل مع dynamic objects
        private string GetStringValueFromDynamic(dynamic field)
        {
            try
            {
                return field?.stringValue?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        private bool GetBoolValueFromDynamic(dynamic field)
        {
            try
            {
                if (field?.booleanValue != null)
                {
                    if (bool.TryParse(field.booleanValue.ToString(), out bool result))
                        return result;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private long GetIntValueFromDynamic(dynamic field)
        {
            try
            {
                if (field?.integerValue != null)
                {
                    if (long.TryParse(field.integerValue.ToString(), out long result))
                        return result;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private DateTime GetDateTimeValueFromDynamic(dynamic field)
        {
            try
            {
                if (field?.timestampValue != null)
                {
                    var timestampStr = field.timestampValue.ToString();
                    if (DateTime.TryParse(timestampStr, out DateTime result))
                    {
                        return result;
                    }
                }
                return DateTime.Now;
            }
            catch
            {
                return DateTime.Now;
            }
        }

        // دوال مساعدة للتعامل مع FirestoreField objects
        private string GetStringValueFromField(FirestoreField field)
        {
            return field?.stringValue ?? "";
        }

        private bool GetBoolValueFromField(FirestoreField field)
        {
            return field?.booleanValue ?? false;
        }

        private long GetIntValueFromField(FirestoreField field)
        {
            if (field?.integerValue != null)
            {
                if (long.TryParse(field.integerValue, out long result))
                    return result;
            }
            return 0;
        }

        private DateTime GetDateTimeValueFromField(FirestoreField field)
        {
            try
            {
                if (!string.IsNullOrEmpty(field?.timestampValue))
                {
                    if (DateTime.TryParse(field.timestampValue, out DateTime result))
                    {
                        return result;
                    }
                }
                return DateTime.Now;
            }
            catch
            {
                return DateTime.Now;
            }
        }

        /// <summary>
        /// إعداد المزامنة التلقائية مع Firestore (محسن)
        /// </summary>
        public async Task<bool> SetupRealtimeSyncAsync()
        {
            try
            {
                if (!isInitialized)
                {
                    await InitializeAsync();
                    if (!isInitialized)
                    {
                        return false;
                    }
                }

                Debug.WriteLine("✅ تم إعداد المزامنة مع Firestore");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إعداد المزامنة التلقائية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// مزامنة ذكية بين البيانات المحلية و Firestore
        /// </summary>
        public async Task<List<ContentItem>> SyncDataAsync(List<ContentItem> localItems)
        {
            try
            {
                Debug.WriteLine("🔄 بدء المزامنة الذكية بين البيانات المحلية و Firestore...");

                if (!isInitialized)
                {
                    Debug.WriteLine("❌ Firestore غير مهيأ للمزامنة");
                    return localItems ?? new List<ContentItem>();
                }

                // تحميل البيانات من Firestore
                var firestoreItems = await LoadDataAsync();

                if (localItems == null || localItems.Count == 0)
                {
                    Debug.WriteLine("ℹ️ لا توجد بيانات محلية - استخدام بيانات Firestore");
                    return firestoreItems;
                }

                if (firestoreItems.Count == 0)
                {
                    Debug.WriteLine("ℹ️ لا توجد بيانات في Firestore - حفظ البيانات المحلية");
                    await SaveDataAsync(localItems);
                    return localItems;
                }

                // دمج البيانات بذكاء
                var mergedItems = MergeDataIntelligently(localItems, firestoreItems);

                Debug.WriteLine($"✅ تم دمج البيانات: {mergedItems.Count} عنصر إجمالي");

                // حفظ البيانات المدموجة في Firestore
                await SaveDataAsync(mergedItems);

                return mergedItems;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في المزامنة الذكية: {ex.Message}");
                return localItems ?? new List<ContentItem>();
            }
        }

        /// <summary>
        /// دمج البيانات المحلية مع بيانات Firestore بذكاء
        /// </summary>
        private List<ContentItem> MergeDataIntelligently(List<ContentItem> localItems, List<ContentItem> firestoreItems)
        {
            try
            {
                Debug.WriteLine($"🔄 دمج {localItems.Count} عنصر محلي مع {firestoreItems.Count} عنصر من Firestore");

                var mergedItems = new List<ContentItem>();
                var processedIds = new HashSet<string>();

                // إضافة العناصر من Firestore أولاً (لها الأولوية)
                foreach (var firestoreItem in firestoreItems)
                {
                    if (!string.IsNullOrEmpty(firestoreItem.Id) && !processedIds.Contains(firestoreItem.Id))
                    {
                        mergedItems.Add(firestoreItem);
                        processedIds.Add(firestoreItem.Id);
                        Debug.WriteLine($"📥 تم إضافة عنصر من Firestore: {firestoreItem.Id}");
                    }
                }

                // إضافة العناصر المحلية التي لا توجد في Firestore
                foreach (var localItem in localItems)
                {
                    if (string.IsNullOrEmpty(localItem.Id))
                    {
                        localItem.Id = Guid.NewGuid().ToString();
                    }

                    if (!processedIds.Contains(localItem.Id))
                    {
                        mergedItems.Add(localItem);
                        processedIds.Add(localItem.Id);
                        Debug.WriteLine($"📤 تم إضافة عنصر محلي جديد: {localItem.Id}");
                    }
                    else
                    {
                        // العنصر موجود في Firestore، تحقق من التحديثات
                        var firestoreItem = mergedItems.FirstOrDefault(f => f.Id == localItem.Id);
                        if (firestoreItem != null && localItem.CreatedDate > firestoreItem.CreatedDate)
                        {
                            // العنصر المحلي أحدث، استبدل العنصر في Firestore
                            var index = mergedItems.IndexOf(firestoreItem);
                            mergedItems[index] = localItem;
                            Debug.WriteLine($"🔄 تم تحديث عنصر بالنسخة المحلية الأحدث: {localItem.Id}");
                        }
                    }
                }

                Debug.WriteLine($"✅ تم دمج البيانات بنجاح: {mergedItems.Count} عنصر إجمالي");
                return mergedItems;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في دمج البيانات: {ex.Message}");
                // في حالة الخطأ، أعد البيانات المحلية
                return localItems;
            }
        }

        /// <summary>
        /// تشفير نص
        /// </summary>
        private string EncryptString(string text)
        {
            try
            {
                // استخدام نفس مفتاح التشفير المستخدم في البرنامج الأصلي
                byte[] key = Encoding.UTF8.GetBytes("InzoIBSecretKey123456789012345678");
                byte[] iv = new byte[16];
                byte[] array;

                using (Aes aes = Aes.Create())
                {
                    aes.Key = key;
                    aes.IV = iv;

                    ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        using (CryptoStream cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                        {
                            using (StreamWriter streamWriter = new StreamWriter(cryptoStream))
                            {
                                streamWriter.Write(text);
                            }
                            array = memoryStream.ToArray();
                        }
                    }
                }

                return Convert.ToBase64String(array);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في تشفير النص: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// فك تشفير نص
        /// </summary>
        private string DecryptString(string cipherText)
        {
            try
            {
                byte[] key = Encoding.UTF8.GetBytes("InzoIBSecretKey123456789012345678");
                byte[] iv = new byte[16];
                byte[] buffer = Convert.FromBase64String(cipherText);

                using (Aes aes = Aes.Create())
                {
                    aes.Key = key;
                    aes.IV = iv;
                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                    using (MemoryStream memoryStream = new MemoryStream(buffer))
                    {
                        using (CryptoStream cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read))
                        {
                            using (StreamReader streamReader = new StreamReader(cryptoStream))
                            {
                                return streamReader.ReadToEnd();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في فك تشفير النص: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// مزامنة إعداد واحد مع Firestore
        /// </summary>
        /// <param name="settingKey">مفتاح الإعداد</param>
        /// <param name="settingValue">قيمة الإعداد</param>
        /// <returns>مهمة غير متزامنة</returns>
        public async Task SyncSettingAsync(string settingKey, object settingValue)
        {
            // فحص الصلاحيات أولاً
            if (!IsAuthorizedToModify())
            {
                Debug.WriteLine("🚫 غير مصرح بمزامنة الإعدادات - تم رفض العملية");
                return;
            }

            if (!isInitialized)
            {
                Debug.WriteLine("⚠️ Firestore غير مهيأ - تم تجاهل مزامنة الإعداد");
                return;
            }

            try
            {
                // توليد أو تجديد رمز المصادقة
                if (!IsTokenValid())
                {
                    GenerateAuthToken();
                }

                var settingDocument = new FirestoreDocument
                {
                    fields = new Dictionary<string, FirestoreField>
                    {
                        ["key"] = new FirestoreField { stringValue = settingKey },
                        ["value"] = new FirestoreField { stringValue = settingValue?.ToString() ?? "" },
                        ["timestamp"] = new FirestoreField { timestampValue = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") },
                        ["deviceId"] = new FirestoreField { stringValue = Environment.MachineName },
                        ["deviceUID"] = new FirestoreField { stringValue = deviceUID },
                        ["authToken"] = new FirestoreField { stringValue = authToken }
                    }
                };

                var json = System.Text.Json.JsonSerializer.Serialize(settingDocument);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{baseUrl}/settings/{settingKey}";
                var response = await httpClient.PatchAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"✅ تم مزامنة الإعداد {settingKey} مع Firestore (مصادق)");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Debug.WriteLine($"❌ فشل مزامنة الإعداد {settingKey} مع Firestore: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في مزامنة الإعداد {settingKey} مع Firestore: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// فئة إعدادات Firestore
    /// </summary>
    public class FirestoreSettings
    {
        public string ProjectId { get; set; }
        public string ApiKey { get; set; }
        public string CollectionName { get; set; }

        // إعدادات الأمان
        public string AuthorizedUID { get; set; }
        public string AuthToken { get; set; }
        public DateTime? TokenExpiry { get; set; }
    }

    /// <summary>
    /// فئة استجابة Firestore
    /// </summary>
    public class FirestoreResponse
    {
        public List<FirestoreDocument> documents { get; set; }
    }

    /// <summary>
    /// فئة مستند Firestore
    /// </summary>
    public class FirestoreDocument
    {
        public string name { get; set; }
        public Dictionary<string, FirestoreField> fields { get; set; }
        public string createTime { get; set; }
        public string updateTime { get; set; }
    }



    /// <summary>
    /// فئة حقل Firestore
    /// </summary>
    public class FirestoreField
    {
        public string stringValue { get; set; }
        public bool? booleanValue { get; set; }
        public string integerValue { get; set; }
        public string timestampValue { get; set; }
    }
}
