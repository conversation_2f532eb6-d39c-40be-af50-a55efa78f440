<Window x:Class="InzoIB_Simple.FirestoreSettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات Cloud Firestore" Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        WindowStyle="SingleBorderWindow"
        ResizeMode="NoResize"
        Background="#FF2B2B2B">

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#FF161642"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#FF4A4A4A"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF007ACC"/>
        <SolidColorBrush x:Key="TextBrush" Color="White"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#FF666666"/>

        <!-- نمط الأزرار -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF005A9E"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط مربعات النص -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderBrush" Value="#FF666666"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="CaretBrush" Value="Black"/>
            <Setter Property="SelectionBrush" Value="#FF0078D4"/>
            <Setter Property="SelectionTextBrush" Value="White"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>

        </Style>

        <!-- نمط كلمات المرور -->
        <Style x:Key="ModernPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderBrush" Value="#FF666666"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="CaretBrush" Value="Black"/>
            <Setter Property="SelectionBrush" Value="#FF0078D4"/>
            <Setter Property="SelectionTextBrush" Value="White"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>

        </Style>

        <!-- نمط بديل بسيط للنص -->
        <Style x:Key="SimpleTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FFFFFFFF"/>
            <Setter Property="Foreground" Value="#FF000000"/>
            <Setter Property="BorderBrush" Value="#FF0078D4"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- نمط بديل بسيط لكلمة المرور -->
        <Style x:Key="SimplePasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Background" Value="#FFFFFFFF"/>
            <Setter Property="Foreground" Value="#FF000000"/>
            <Setter Property="BorderBrush" Value="#FF0078D4"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="🔥 إعدادات Cloud Firestore" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Foreground="{StaticResource TextBrush}"
                       HorizontalAlignment="Center"/>
            <TextBlock Text="قم بإدخال معلومات مشروع Firebase الخاص بك" 
                       FontSize="14" 
                       Foreground="#FFAAAAAA"
                       HorizontalAlignment="Center"
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- معرف المشروع -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="🆔 معرف المشروع (Project ID)"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="{StaticResource TextBrush}"
                               Margin="0,0,0,5"/>
                    <TextBox x:Name="ProjectIdTextBox"
                             Background="White"
                             Foreground="Black"
                             BorderBrush="Blue"
                             BorderThickness="2"
                             FontSize="14"
                             Height="45"
                             Padding="10,12,10,8"
                             VerticalContentAlignment="Center"
                             Text=""
                             ToolTip="أدخل معرف مشروع Firebase هنا"
                             GotFocus="TextBox_GotFocus"
                             TextChanged="TextBox_TextChanged"/>
                    <TextBlock Text="مثال: my-firebase-project-123"
                               FontSize="12"
                               Foreground="#FF888888"
                               Margin="0,3,0,0"/>
                </StackPanel>

                <!-- مفتاح API -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="🔑 مفتاح API (API Key)" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="{StaticResource TextBrush}"
                               Margin="0,0,0,5"/>
                    <PasswordBox x:Name="ApiKeyPasswordBox"
                                 Background="White"
                                 Foreground="Black"
                                 BorderBrush="Blue"
                                 BorderThickness="2"
                                 FontSize="14"
                                 Height="45"
                                 Padding="10,12,10,8"
                                 VerticalContentAlignment="Center"
                                 ToolTip="أدخل مفتاح API من Google Cloud Console"
                                 GotFocus="PasswordBox_GotFocus"
                                 PasswordChanged="PasswordBox_PasswordChanged"/>
                    <TextBlock Text="احصل على مفتاح API من Firebase Console"
                               FontSize="12"
                               Foreground="#FF888888"
                               Margin="0,3,0,0"/>
                </StackPanel>

                <!-- اسم المجموعة -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="📁 اسم المجموعة (Collection Name)"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="{StaticResource TextBrush}"
                               Margin="0,0,0,5"/>
                    <TextBox x:Name="CollectionNameTextBox"
                             Background="White"
                             Foreground="Black"
                             BorderBrush="Blue"
                             BorderThickness="2"
                             FontSize="14"
                             Height="45"
                             Padding="10,12,10,8"
                             VerticalContentAlignment="Center"
                             Text="inzo_content"
                             ToolTip="اسم المجموعة في Firestore"
                             GotFocus="TextBox_GotFocus"
                             TextChanged="TextBox_TextChanged"/>
                    <TextBlock Text="اسم المجموعة التي ستحفظ فيها البيانات"
                               FontSize="12"
                               Foreground="#FF888888"
                               Margin="0,3,0,0"/>
                </StackPanel>

                <!-- معلومات إضافية -->
                <Border Background="#FF1E1E1E" 
                        BorderBrush="{StaticResource BorderBrush}" 
                        BorderThickness="1" 
                        CornerRadius="8" 
                        Padding="15"
                        Margin="0,10,0,0">
                    <StackPanel>
                        <TextBlock Text="ℹ️ معلومات مهمة" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Foreground="{StaticResource AccentBrush}"
                                   Margin="0,0,0,10"/>
                        
                        <TextBlock TextWrapping="Wrap" 
                                   FontSize="13" 
                                   Foreground="#FFCCCCCC"
                                   LineHeight="20">
                            <Run Text="• للحصول على معرف المشروع: اذهب إلى Firebase Console → Project Settings"/>
                            <LineBreak/>
                            <Run Text="• للحصول على مفتاح API: اذهب إلى Google Cloud Console → APIs &amp; Services → Credentials"/>
                            <LineBreak/>
                            <Run Text="• تأكد من تفعيل Firestore API في مشروعك"/>
                            <LineBreak/>
                            <Run Text="• سيتم تشفير وحفظ هذه المعلومات محلياً"/>
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- حالة الاتصال -->
                <StackPanel x:Name="StatusPanel" Visibility="Collapsed">
                    <TextBlock x:Name="StatusText" 
                               FontSize="14" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Margin="0,10,0,0"/>
                </StackPanel>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            
            <Button x:Name="TestConnectionButton" 
                    Content="🔍 اختبار الاتصال" 
                    Style="{StaticResource ModernButtonStyle}"
                    Width="150"
                    Margin="0,0,10,0"
                    Click="TestConnectionButton_Click"/>
            
            <Button x:Name="SaveButton" 
                    Content="💾 حفظ الإعدادات" 
                    Style="{StaticResource ModernButtonStyle}"
                    Width="150"
                    Margin="0,0,10,0"
                    Click="SaveButton_Click"/>
            
            <Button x:Name="CancelButton" 
                    Content="❌ إلغاء" 
                    Style="{StaticResource ModernButtonStyle}"
                    Width="100"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
